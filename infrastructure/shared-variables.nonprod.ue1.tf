#==============================================================
# Shared Variables for US-East-1 in Non-Production Account
#==============================================================

variable "vpc_name" {
  description = "VPC Name"
  default     = "awsaaianp2"
}

variable "rr_vpc_name" {
  description = "Rates and Residuals VPC Name"
  default     = "awsaaianp"
}

variable "availability_zones" {
  description = "Available Availability Zones for this Region"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "rds_seed_from_snapshot_ids" {

	description = "Most recent Snapshot Identifiers as optional seed data when creating an RDS instances in nonprod"
	type        = map(string)
	default     = {
		"incentives-us" = "arn:aws:rds:us-east-1:************:cluster-snapshot:incentives-us-master-for-nonprod-environments-********"
		"incentives-ca" = "arn:aws:rds:us-east-1:************:cluster-snapshot:incentives-ca-master-for-nonprod-environments-********"
		"mbasice"       = "arn:aws:rds:us-east-1:************:cluster-snapshot:mbasice-master-for-nonprod-environments-20240709"
	}

}

variable "nfs_cidr" {
  description = "The CIDR block of the internal aws network (vpc) for NFS usage."
  default     = "***********/22"
}

