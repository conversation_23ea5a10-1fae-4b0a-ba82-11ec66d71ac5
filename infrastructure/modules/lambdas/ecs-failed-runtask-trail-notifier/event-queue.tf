resource "aws_sqs_queue" "failed_runtask_events" {
  name                       = "${var.application}-${var.environment}-ecs-failed-runtask-trail"
  visibility_timeout_seconds = 300   # 5min, same as notify lambda timeout
  message_retention_seconds  = 86400 # 1 day
  receive_wait_time_seconds  = 5

  tags = {
    Application  = var.application
    Environment  = var.environment
    Release      = var.build_number
    Component    = var.component
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "sqs-${var.region_abbreviated}-${var.application_abbreviated}-${var.environment}-ecs-failed-runtask-trail"
  }
}

