resource "aws_lambda_function" "default" {
  filename         = var.lambda_package_path
  function_name    = "${var.application}-${var.environment}-ecs-task-state-forwarder"
  description      = "Receives events from ECS task state and forwards them for historical logging"
  role             = var.lambda_role_arn[var.account_type]
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  source_code_hash = filebase64sha256(var.lambda_package_path)
  memory_size      = 128
  timeout          = 300

  environment {
    variables = {
      Region      = var.region
      Environment = var.environment
    }
  }

  lifecycle {
    ignore_changes = [tags.LaunchedOn]
  }

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    Component    = var.component
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Name         = "lm-${var.region_abbreviated}-${var.application_abbreviated}-${var.environment}-ecs-task-state-forwarder"
  }
}

resource "aws_cloudwatch_log_group" "default" {
  name              = "/aws/lambda/${var.application}-${var.environment}-ecs-task-state-forwarder" # Same as function name
  retention_in_days = 7
}

resource "aws_cloudwatch_log_subscription_filter" "splunk" {
  # Forwards logs to Splunk under: index="aws" sourcetype="aws:cloudwatchlogs:lambda" log_group="/aws/lambda/ais10-{nonprod || prod}-ecs-task-state-forwarder"
  name            = "${var.application}-${var.environment}-ecs-task-state-forwarder-subscription-to-splunk"
  log_group_name  = aws_cloudwatch_log_group.default.name
  filter_pattern  = ""
  destination_arn = "arn:aws:logs:${var.region}:************:destination:${var.account_name}-cloudwatch-lambda-destination-${var.region}"
}

resource "aws_lambda_permission" "default" {
  statement_id  = "${aws_cloudwatch_event_rule.default.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.default.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.default.arn
}

resource "aws_cloudwatch_event_rule" "default" {
  name        = "${var.application}-${var.environment}-ecs-task-state-forwarder"
  description = "Generates events from ECS task state and forwards them for historical logging"
  is_enabled  = var.account_type == "nonprod" ? false : true

  lifecycle {
    ignore_changes = [is_enabled]
  }

  event_pattern = <<PATTERN
{
  "source": [
    "aws.ecs"
  ],
  "detail-type": [
    "ECS Task State Change"
  ]
}
PATTERN

}

resource "aws_cloudwatch_event_target" "default" {
  rule = aws_cloudwatch_event_rule.default.name
  arn  = aws_lambda_function.default.arn
}

