#!/bin/bash

# Update and install packages
yum -y update
yum -y install nfs-utils wget

# Configure and install yum-update
yum install -y yum-cron
sed -i 's/^update_cmd.*=.*$/update_cmd = security/' /etc/yum/yum-cron.conf
sed -i 's/^update_messages.*=.*$/update_messages = yes/' /etc/yum/yum-cron.conf
sed -i 's/^download_updates.*=.*$/download_updates = yes/' /etc/yum/yum-cron.conf
sed -i 's/^apply_updates.*=.*$/apply_updates = yes/' /etc/yum/yum-cron.conf
service yum-cron start
chkconfig yum-cron on

# Replace NTP with Chrony for Clock Drift
yum -y erase 'ntp*'
yum -y install chrony

ChronyConf="/etc/chrony.conf"
ChronyDesiredServerConfigLine="server 169.254.169.123 prefer iburst minpoll 4 maxpoll 4"
ChronyServerConfigLine=$(grep -n "^server " $ChronyConf)
if [ $? -eq 0 ]
then
  echo "Updating server in chrony $ChronyConf"
  ChronyReplaceLineNumber=$(echo -n $ChronyServerConfigLine | tail -n 1 | awk --field-separator=":" '{print $1}')
  sed -i "$${ChronyReplaceLineNumber}s/.*/$${ChronyDesiredServerConfigLine}/" $ChronyConf
else
  echo "Inserting server in chrony $ChronyConf"
  sed -i "1s/^/\n/" $ChronyConf
  sed -i "1s/^/$${ChronyDesiredServerConfigLine}\n/" $ChronyConf
  sed -i "1s/^/# NTP server from AWS\n/" $ChronyConf
fi

service chronyd start
chkconfig chronyd on

# Install SSM Agent
yum install -y https://s3.amazonaws.com/ec2-downloads-windows/SSMAgent/latest/linux_amd64/amazon-ssm-agent.rpm

# Setup CloudWatchMonitor for Memory Metrics
yum -y install python-pip
pip -q install cloudwatchmon
crontab -l | { cat; echo "* * * * * mon-put-instance-stats.py --mem-util --mem-used --mem-avail --disk-space-util --disk-path=/ --from-cron"; } | crontab -

# Mount aisdata for reference files
mkdir -p /aisdata/
chown 48:48 /aisdata/
echo "${efs_id}.efs.${region}.amazonaws.com:/ /aisdata nfs4 nfsvers=4.1,noresvport,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2 0 0" | tee -a /etc/fstab
mount -a -t nfs4

# Put the ecs.config file in the /etc/ecs folder to associate ec2 to the cluster
cat >/etc/ecs/ecs.config <<EOL
ECS_CLUSTER=${ecs_cluster_name}
ECS_AVAILABLE_LOGGING_DRIVERS=${ecs_logging}
ECS_ENABLE_TASK_IAM_ROLE=true
ECS_ENABLE_CONTAINER_METADATA=true
ECS_ENGINE_TASK_CLEANUP_WAIT_DURATION=10m
EOL
