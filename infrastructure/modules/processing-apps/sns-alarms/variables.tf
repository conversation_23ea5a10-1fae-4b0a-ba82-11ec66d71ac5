###############################################################
# Pass-through Variables
###############################################################

variable "application" {
}

variable "service" {
}

variable "region" {
}

variable "environment" {
}

variable "launched_by" {
}

variable "launched_on" {
}

variable "slack_contact" {
}

variable "build_number" {
}

variable "component" {
}

###############################################################
# Cloudwatch Alarm Variables
###############################################################

variable "ddbv2_long_running_oem_warning" {
  description = "The threshold value to use on the Critical Long Running OEMs in DDBv2 Queue alarm. Denoted in seconds"
  default     = "1"
}

variable "rrri_long_running_oem_warning" {
  description = "The threshold value to use on the Critical Long Running OEMs in RRRI Queue alarm. Denoted in seconds"
  default     = "1"
}

variable "vin_process_alarm_warning" {
  description = "The Warning threshold value to use on the VIN Process pending Queue alarm. Denoted in number of vins"
  default     = "1000"
}

variable "vin_process_alarm_critical" {
  description = "The Critical threshold value to use on the VIN Process pending Queue alarm. Denoted in number of vins"
  default     = "10000"
}

variable "warning_alert_arn" {
  description = "ARN of the warning alert SNS topic"
}

variable "critical_alert_arn" {
  description = "ARN of the critical alert SNS topic"
}

variable "rrri_critical_alert_arn" {
  description = "ARN of the rrri critical alert SNS topic"
}

variable "cw_alarm_ftp_threshold" {
  description = "The threshold value to use on the FTP alarm."
  default     = "0"
}

variable "cw_alarm_ftp_evaluation_periods" {
  description = "The evaluation periods value to use on the FTP alarm."
  default     = "1"
}

variable "cw_alarm_ftp_periods" {
  description = "The periods value to use on the FTP alarm."
  default     = "60"
}

