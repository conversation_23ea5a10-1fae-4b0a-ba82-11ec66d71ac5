[{"TerraformVariableName": "docker_image_uri", "CakeArgumentName": "docker_image_uri", "IsOptional": true, "ValidationRegex": "^[0-9]+\\.dkr\\.ecr\\.[a-z0-9-]+\\.amazonaws\\.com\\/[a-zA-Z0-9_-]+:[a-zA-Z0-9._-]+$", "ValidationFailedText": "must be a valid ECR image URI format (account.dkr.ecr.region.amazonaws.com/repository:tag)"}, {"TerraformVariableName": "db_host", "CakeArgumentName": "db_host", "IsOptional": false, "ValidationRegex": "^[a-zA-Z0-9.-]+$", "ValidationFailedText": "must be a valid database hostname"}, {"TerraformVariableName": "db_database", "CakeArgumentName": "db_database", "IsOptional": false, "ValidationRegex": "^[a-zA-Z0-9_-]+$", "ValidationFailedText": "must be a valid database name"}, {"TerraformVariableName": "db_user", "CakeArgumentName": "db_user", "IsOptional": false, "ValidationRegex": "^[a-zA-Z0-9_-]+$", "ValidationFailedText": "must be a valid database username"}, {"TerraformVariableName": "db_password", "CakeArgumentName": "db_password", "IsOptional": false, "ValidationRegex": "^.{8,}$", "ValidationFailedText": "must be at least 8 characters long"}, {"TerraformVariableName": "email_smtp_user", "CakeArgumentName": "email_smtp_user", "IsOptional": false, "ValidationRegex": "^[A-Z0-9]{20}$", "ValidationFailedText": "must be a valid AWS SES SMTP username (20 character alphanumeric)"}, {"TerraformVariableName": "email_smtp_password", "CakeArgumentName": "email_smtp_password", "IsOptional": false, "ValidationRegex": "^.{20,}$", "ValidationFailedText": "must be at least 20 characters long"}, {"TerraformVariableName": "email_to_emails", "CakeArgumentName": "email_to_emails", "IsOptional": true, "ValidationRegex": "^\\[.*\\]$", "ValidationFailedText": "must be a JSON array of email addresses"}, {"TerraformVariableName": "dataone_s3_bucket", "CakeArgumentName": "dataone_s3_bucket", "IsOptional": false, "ValidationRegex": "^[a-z0-9.-]{3,63}$", "ValidationFailedText": "must be a valid S3 bucket name"}, {"TerraformVariableName": "enable_scheduled_tasks", "CakeArgumentName": "enable_scheduled_tasks", "IsOptional": true, "ValidationRegex": "^(true|false)$", "ValidationFailedText": "must be either 'true' or 'false'"}]