# Infrastructure Documentation

## Terminology    
| Name | Meaning |
| --- | --- |
| Environment | A collection of components that together form a whole environment |
| Component | An individually deployable Terraform root module with its own tfstate file |
| Root Module | Terraform term referring to the working directory that Terraform gets executed against (i.e. a component) |

## Directory Structure

**infrastructure/modules/:**   
Contains all the modules for all environment components. The directory structure for organizing the modules can be flexible since modules are referenced by a specific path.

**infrastructure/environments/:**   
Contains a sub-directory for every environment that we want to be able to deploy with an independent set of infrastructure. The environment directories in turn contain sub-directories for every individually deployable component that makes up the particular environment. Many of the components for similar environments (i.e. non-staging/production) are the exact same copy/pasta Terraform scripts but this setup gives us the ability to manage them independently without affecting other environments.

**infrastructure/configs/components/:**   
Contains at most one JSON file for every distinct component across environments which are named after the component's (directory) name. These JSON files define required Terraform command-line arguments (that need collected from Cake) which are specific to a component and apply to all components named the same across all environments. The Cake script understands these JSON files which allows us to avoid duplicating the Cake logic for every component that requires unique arguments. It's possible for a component to not be represented in this directory if it only depends on the global/shared variables which are automatically included without needing defined in the JSON file. More information is mentioned below when discussing deploying components.

## Terraform Variable File Organization
The variable files are the Terraform files that only contain `variable` definitions. The variables for a component are collectively composed of the single `variables.tf` file located in each environment/component directory and three shared variable files located in the `infrastructure/` directory. When deploying a particular component, the Terraform files in its directory are the only scripts that get executed so the Cake build script copies in the shared variable files (three total for global/account/region) before making any Terraform calls. By creating these shared variable files that get copied into each component at the time of deployment, it allows us to define variables across all components to avoid maintaining a large amount of duplicate code. 

**shared-variables.global.tf:**   
- Gets copied into environment/component folder as shared-variables-global.tf by the Cake build script
- Meant for variables that are used across every component for all accounts, regions, and their environments
- Useful for globally storing default values (such as the name of the application) and defining required values that need passed in as Terraform arguments (such as the name of the environment or the build number)

**shared-variables.(nonprod or prod).tf:**   
- Gets copied into environment/component folder as shared-variables-account.tf by the Cake build script
- Meant for variables that are specific to a particular account and across all its regions (IAM Roles, DNS, etc)
- Could hold region-specific variables only if it's a map type that's keyed off the region name (won't work for list value types [i.e. subnets] which are always in region-specific files)
- Both of these account-specific variable files should have the exact same variable definitions with varying default values based on the account
- None of the variables in these two files should be required (default definitions only); otherwise, a single definition would be all that's needed which would belong in shared-variables.global.tf

**shared-variables.(nonprod or prod).(region).tf:**   
- Gets copied into environment/component folder as shared-variables-region.tf by the Cake build script
- Meant for variables that are specific to a particular region in a particular account (VPC, Subnets, etc)
- All of the region-specific variable files should have the exact same variable definitions with varying default values based on the account and region
- None of the variables in these files should be required (default definitions only); otherwise, a single definition would be all that's needed which would belong in shared-variables.global.tf

**variables.tf (in each environment/component directory):**   
- Contains only variables that are specific to the root module
- As a tip for creating this file, it's also useful for defining a) repeated values across multiple module instances and b) special strings to interpolate into module parameters to call out their meaning

## Terraform Modules
Modules are a way to create re-usable chunks of Terraform code. If possible, it's useful to make them as broad/reusable as possible to decouple them from their specifically intended use (which is easier to do for some resource types than others). Often the modules will be very specific to a set of infrastructure which is still useful because it allows us to repeat the code across environments. As an example, if you needed to script some DynamoDB tables, the Terraform code will be very specific by nature so it's not reusable in a sense that it's unlikely for more than one component in an environment to reference the module.

**Organization Tips**    
- Ensure resource names/identifiers are unique by including at least the `application` (name) and `environment` variables in the name; however, be careful to not include variables such as the `build_number` which would cause the resource to re-create itself every deployment (which would likely be real bad).
- Each module should have its `outputs.tf` file for returning values to reference in other modules/resources in calling root module

**Pass-Through Variables**   
- Term that we started using in comments for a particular set of required variables in a module that are identifiable by their lack of `default` and `description` attributes
- These variables are required for a module but already defined with a `description` attribute elsewhere (in one of the shared variable or component variable files) so they look like empty definitions
- The other variables in the module's `variable.tf` file should all have descriptions and will be variables specific to only the module to keep the scoping as private as possible

## Deploying Components with Cake
Configuring a component for deployment with Cake is simple since we abstracted the configuration variables/arguments for components into JSON files (located at `infrastructure/configs/components/`) so you don't actually need to create/edit a Cake script for each one. In rare cases, the Cake script may need to do some custom tasks only for a particular component which may lead to copy/pasta of `build/deployTerraformComponent.cake` but hopefully it can be avoided if it ever comes up. Another scenario that could arise is needing to override the (default) configuration JSON for a particular environment/component combination which can be done by placing an override file called `config.json` in the component  sub-directory within the appropriate environment directory.

Example of a component's configuration JSON file:   
```
[
	{
		"TerraformVariableName" : "memcached_replica_pair_count",
		"CakeArgumentName"      : "memcached_replica_pair_count",
		"ValidationRegex"       : "^[12345]{1}$",
		"ValidationFailedText"  : "must deploy between 1 and 5 pairs"
	}
]
```

Explaination of JSON keys:  
 
| Key Name | Usage |
| --- | --- |
| TerraformVariableName | The name of a variable from the component's `variable.tf` file that needs passed in from Cake whenever it invokes Terraform. |
| CakeArgumentName | The name of the argument or environment variable that Cake will read as the source of the value to pass to the associated `TerraformVariableName`. These are normally defined in Team City as environment variables for the deployment, but it also checks as an argument as well since it makes local testing easier at the very least. |
| ValidationRegex | Regular expression for validating the value before its passed to Terraform. This key is required so at a minimum it should be set to `^.+$` to represent one or more of any characters. |
| ValidationFailedText | The error message to report in the build logs if the value from `TerraformVariableName` doesn't match the `ValidationRegex`. The first task that Cake runs each build is safety checks which is where these validations are tested - and if any of the configuration definitions fails its validation then it throws an exception to stop the build and report the issue. |

## Multi-Region Support
We currently support multiple regions in our Cake and Terraform scripts with the intention of using a secondary region within an account as a hot/cold swap. If a region ever goes down, we will deploy all our infrastructure to another region at that time and switch over (details are still being discussed). There are a few resources, mostly the manually deployed ones such as base AMIs and case-by-case exceptions, which we deploy to all regions every time to avoid dealing with them as an additional task at the time of failover. All of our Terraform scripts have been recently updated to require a region to be provided as an argument from Cake, and Cake also accepts a region as an argument which defaults to us-east-1 for both accounts. The Cake script for all Terraform infrastructure essentially deploys to an account and region combination as well as being labeled as a particular environment. Currently the Team City builds have not been updated to add region as a parameter (approach hasn't been decided yet) so they continue to deploy to the default of us-east-1.

**Currently Supported Regions**  

| Account | Regions |
| --- | --- |
| Production | us-east-1, us-west-2 |
| Non-Production | us-east-1 |

**Naming Convention Concerns**  
When naming resources in AWS, make sure to understand the name scoping that particular service as per AWS documentation. Even though we intend to run our regions hot/cold, if we ever failover, the production account's infrastructure for both regions would exist at the same time where us-west-2 would be deployed as hot and us-east-1 would be cold since it's the reason we'd be failing over. So we need to ensure that at the time of failover, we don't get Terraform errors in the "apply" phase as a result of a conflict between duplicate resource names. Luckily, for most resources, they're scoped to the region so the names don't need to contain the region name in order to avoid conflicts. However, for resources created by Terraform that tend to be more global in nature and thus scoped across all regions for the account, it will be necessary to factor in the region's abbreviated name into its naming convention.

As an example of a more complicated resource type when it comes to naming with multi-region support would be the S3 storage service. The bucket names in S3 are associated to a single region; however, the bucket names themselves are globally unique across regions. In order to support multiple regions with S3, as well as multiple accounts, the names need to reflect both the account and region name (i.e. a bucket called "standard-rates.packages.prod.ue1"). 

## Manually Created Resources

**Belonging to VPC**

EFS: 
Security Group (in region/vpc): ais10-efs-access [outbound: All 0.0.0.0/0, inbound: tcp 2049 0.0.0.0/0 or specific cidr ideally]  
File System (in region/vpc): efs-multi-ais10ec2stack-ue1-np1.0.0 [using all subnets w/ ais10-efs-access security group for each subnet]  

