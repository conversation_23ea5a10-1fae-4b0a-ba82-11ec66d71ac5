#===========================================================
# Shared Variables for US-East-1 in Production Account
#===========================================================

variable "vpc_name" {
  description = "VPC Name"
  default     = "awsaaia3"
}

variable "rr_vpc_name" {
  description = "Rates & Residuals VPC Name"
  default     = "awsaaia"
}

variable "availability_zones" {
  description = "Available Availability Zones for this Region"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d"]
}

variable "nfs_cidr" {
  description = "The CIDR block of the internal aws networks for NFS usage."
  default     = "************/23"
}

variable "certificate_arn" {
  description = "SSL Cert to use for consumer webstack"
  default     = "arn:aws:acm:us-east-1:************:certificate/da6d41eb-edde-4a6a-be26-87e602904924"
}

