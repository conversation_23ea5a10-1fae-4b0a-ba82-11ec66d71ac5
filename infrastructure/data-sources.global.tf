#==============================================================
# Data Sources
#==============================================================

# Find the VPC
data "aws_vpc" "vpc" {
  filter {
    name   = "tag:Name"
    values = [var.vpc_name]
  }
}

# Find the VPC (rates & residuals VPC)
data "aws_vpc" "rr_vpc" {
  filter {
    name   = "tag:Name"
    values = [var.rr_vpc_name]
  }
}

# Find the Public Subnets for the VPC
data "aws_subnet_ids" "public" {
  vpc_id = data.aws_vpc.vpc.id

  tags = {
    SUB-Type = "Public"
  }
}

# Find the Private Subnets for the VPC
data "aws_subnet_ids" "private" {
  vpc_id = data.aws_vpc.vpc.id

  tags = {
    SUB-Type = "Private"
  }
}

# Find the Private Subnet details for the set chosen where the Incentives DBs reside
data "aws_subnet" "rds_master_incentives_primary" {
  id = element(tolist(data.aws_subnet_ids.private.ids), 0)
}

data "aws_subnet" "rds_master_incentives_secondary" {
  id = element(tolist(data.aws_subnet_ids.private.ids), 1)
}

# Setup the Parameter Store for the NewRelic and PagerDuty keys/APIs
data "aws_ssm_parameter" "newrelic_license_key" {
  name = "/all/newrelic_license_key"
}

data "aws_ssm_parameter" "newrelic_api_key" {
  name = "/all/newrelic_api_key"
}

data "aws_ssm_parameter" "pagerduty_api_key" {
  name = "/all/pagerduty_token"
}

data "aws_ssm_parameter" "incentives_db_pw" {
  name = "incentives-db-password"
}

