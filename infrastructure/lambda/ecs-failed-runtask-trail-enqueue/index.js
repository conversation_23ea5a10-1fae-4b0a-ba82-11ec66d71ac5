'use strict';

const { SQSClient, SendMessageCommand } = require('@aws-sdk/client-sqs');
const { ECSClient, DescribeClustersCommand } = require('@aws-sdk/client-ecs');
const Polly = require('polly-js');
const _ = require('underscore');

const SQS = new SQSClient({ region: process.env.Region });
const ECS = new ECSClient({ region: process.env.Region });

exports.handler = async (event, context) => {

	// Ignore successful events (CloudWatch rules don't allow filtering on these particular child properties of the event)
	if (!event.detail.responseElements || (event.detail.responseElements.failures.length === 0 && event.detail.responseElements.tasks.length > 0)) {
		console.log(`Ignored event ${event.detail.eventID}`);
		return false;
	}

	// Build the event data needed to store in the queue
	const failureEvent = {
		eventId: event.detail.eventID,
		timestamp: event.detail.eventTime,
		clusterName: _.last(event.detail.requestParameters.cluster.split('/')),
		taskName: _.last(event.detail.requestParameters.taskDefinition.split('/')),
		registeredNodes: null,
		failures: {
			failedNodes: 0,
			reasons: []
		}
	};

	const failuresByNode = _.groupBy(event.detail.responseElements.failures, node => node.arn);

	for (let node in failuresByNode) {
		if (!failuresByNode.hasOwnProperty(node)) continue;

		let nodeFailureReasons = _.pluck(failuresByNode[node], 'reason');
		for (let i = 0, il = nodeFailureReasons.length; i < il; i++) {
			if (!failureEvent.failures.reasons.includes(nodeFailureReasons[i])) {
				failureEvent.failures.reasons.push(nodeFailureReasons[i]);
			}
		}

		failureEvent.failures.failedNodes++;
	}

	// Find how many container instances are registered/available in the cluster (includes ones being drained)
	try {
		if (!event.detail.requestParameters || !_.isString(event.detail.requestParameters.cluster))
			throw new Error('Could not find property `cluster` in `event.detail.requestParameters` to lookup cluster details');

		const clusterArn = event.detail.requestParameters.cluster;
		const clusterDetails = await Polly().waitAndRetry(3).executeForPromise(async () => {
			return ECS.send(new DescribeClustersCommand({ clusters: [clusterArn], include: [] }));
		});

		if (clusterDetails && _.isArray(clusterDetails.clusters) && clusterDetails.clusters.length === 1) {
			failureEvent.registeredNodes = clusterDetails.clusters[0].registeredContainerInstancesCount;
		} else {
			throw new Error(`Could not lookup details for single cluster ${clusterArn}: ` + JSON.stringify(clusterDetails));
		}
	}
	catch (err) {
		console.warn(`${err.code || 'Exception'}: ${err.message}`);
	}

	// Store failure event in the queue
	console.log(`Attempting to enqueue event ${failureEvent.eventId}`);
	await Polly().waitAndRetry(3).executeForPromise(async () => {
		return SQS.send(new SendMessageCommand({ QueueUrl: process.env.SqsQueueUrl, MessageBody: JSON.stringify(failureEvent) }));
	});

	console.log(`Enqueued event ${failureEvent.eventId}`);
	return true;
};