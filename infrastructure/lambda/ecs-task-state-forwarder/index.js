'use strict';

const { SSMClient, GetParametersCommand } = require("@aws-sdk/client-ssm");
const Slack = require('slack-node');
const _ = require('underscore');

const SSM = new SSMClient({ region: process.env.Region });

exports.handler = async (event, context, callback) => {
	const clusterName = _.last(event.detail.clusterArn.split('/'));
	const taskDefinitionName = event.detail.group.substring('family:'.length);

	// Function to fetch multiple values under a shared path from parameter store in a single call.
	const getParametersFromBasePath = async (parameterPath, parameterNames) => {
		const parameters = parameterNames.map(name => parameterPath.concat(name));

		try {
			let request = { Names: parameters, WithDecryption: true };

			let response = await SSM.send(new GetParametersCommand(request));
			if (response.InvalidParameters.length) {
				throw new Error('Unable to find parameters: ' + response.InvalidParameters.join(', '));
			}

			let result = response.Parameters
				.map(parameter => {
					return { Name: parameter.Name.substring(parameterPath.length), Value: parameter.Value };
				})
				.reduce((parameterGroup, parameter) => {
					parameterGroup[parameter.Name] = parameter.Value;
					return parameterGroup;
				}, {});

			return result;
		}
		catch (err) {
			console.log(`${err.code || 'Exception'}: ${err.message}`);
			return null;
		}
	};

	// Function to send slack notification with event info
	const sendSlackNotification = async (eventData, problemSummary) => {
		const slackChannelParameterName = 'channel_name';
		const slackWebhookParameterName = 'webhook_url';
		const slackAuthTokenParameterName = 'auth_token';

		const parameters = await getParametersFromBasePath('/all/slack/operations/', [slackChannelParameterName, slackWebhookParameterName, slackAuthTokenParameterName]);
		if (parameters === null) return;

		const slackClient = new Slack();
		slackClient.setWebhook(parameters[slackWebhookParameterName].concat(parameters[slackAuthTokenParameterName]));
		slackClient.webhook(
			{
				channel: parameters[slackChannelParameterName],
				username: "ECS Task State Notifier",
				attachments: [
					{
						pretext: problemSummary,
						fields: [
							{
								title: "Cluster Name",
								value: clusterName,
								short: false
							},
							{
								title: "Container Instance Id",
								value: _.last(eventData.detail.containerInstanceArn.split('/')),
								short: false
							},
							{
								title: "Task Name",
								value: taskDefinitionName,
								short: false
							},
							{
								title: "Started At",
								value: eventData.detail.startedAt ? eventData.detail.startedAt : "N/A",
								short: true
							},
							{
								title: "Stopped At",
								value: eventData.detail.stoppedAt,
								short: true
							},
							{
								title: "Stop Code",
								value: eventData.detail.stopCode ? eventData.detail.stopCode : "N/A",
								short: true
							},
							{
								title: "Stop Reason",
								value: eventData.detail.stoppedReason,
								short: true
							}
						],
						color: _.isString(process.env.Environment) && process.env.Environment.startsWith("prod") ? "#E23010" : "#EAEA00",
						mrkdwn_in: ['pretext']
					}
				]
			},
			function (err, response) {
				if (err) console.log(err);
			}
		);
	};

	// If we discover any stopped tasks with failed containers then we need to notify the team via slack.
	// We consider failed containers as those which are in a stopped status with an exit code that's undefined or non-zero.
	// When a task fails for reasons like scheduling issues in ECS, it won't provide an exit code since it failed prior to even starting the container's command.
	const isTaskStopped = event.detail.lastStatus.toUpperCase() === "STOPPED";
	const hasFailedContainers = _.some(event.detail.containers, container => container.lastStatus.toUpperCase() === "STOPPED" && container.exitCode !== 0);

	if (isTaskStopped && hasFailedContainers) {
		const failedContainers =
			_.chain(event.detail.containers)
				.filter(container => container.lastStatus.toUpperCase() === "STOPPED" && container.exitCode !== 0)
				.map(container => {
					return {
						Name: container.name || "Unknown",
						ExitCode: container.exitCode,
						Reason: container.reason,
						TaskArn: container.taskArn || ""
					};
				})
				.value();

		let problemSummary;
		if (failedContainers.length > 1) {
			problemSummary = "*Containers stopped with non-zero exit codes*\n";
		} else {
			problemSummary = "*Container stopped with non-zero exit code*\n";
		}

		failedContainers.forEach(container => {
			let status = container.ExitCode !== undefined ? container.ExitCode : container.Reason;
			let taskId = _.last(container.TaskArn.split('/'));

			// Generate task log link based on the cluster name because each cluster can define their own naming convention for the log group
			let logUrl = null;
			if (clusterName.includes("processing-apps-cluster")) {
				logUrl = `https://${event.region}.console.aws.amazon.com/cloudwatch/home?region=${event.region}#logEventViewer:group=${taskDefinitionName};stream=ecstasks/${container.Name}/${taskId}`;
			}

			// Build a pretext line for each container in the task
			if (logUrl === null) {
				problemSummary += `${container.Name} (${taskId}): ${status}\n`;
			} else {
				problemSummary += `${container.Name} (<${logUrl}|${taskId}>): ${status}\n`;
			}
		});

		await sendSlackNotification(event, problemSummary);
	}

	// Capture the entire event shape so it forwards to Splunk for searching 
	console.log('JSON: ' + JSON.stringify(event));

	callback(null, "Forwarded ECS task state event");
};