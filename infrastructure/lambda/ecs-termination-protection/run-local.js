#!/usr/bin/env node

// Load the lambda
const Index = require('./index.js');
const context= {
  functionVersion: '$LATEST',
  functionName: 'ecs-termination-protection',
  memoryLimitInMB: '128',
  logGroupName: '/aws/lambda/ecs-termination-protection',
  logStreamName: '2020/01/17/[$LATEST]99e9ada8c2bf43c0976d78618019ca77',
  clientContext: undefined,
  identity: undefined,
  invokedFunctionArn: 'arn:aws:lambda:us-east-1:************:function:ecs-termination-protection',
  awsRequestId: '********-1404-41fc-a825-6e80ef70b036',
}
// CloudWatch Event Target
const EventPayload = {
  version: '0',
  id: '8cd15038-8d7e-03a4-8f13-4a5e64ce6227',
  'detail-type': 'EC2 Instance-terminate Lifecycle Action',

  source: 'aws.autoscaling',
  account: '************',
  time: '2020-01-17T07:16:12Z',
  region: 'us-east-1',
  resources: [
    'arn:aws:autoscaling:us-east-1:************:autoScalingGroup:2d9ce41e-bbfc-4e42-acc5-a0c71d7825a3:autoScalingGroupName/ais10-scratch-alpha-processing-apps-cluster-asg'
  ],
  detail: {
    LifecycleActionToken: '415a9e5e-2c5c-4120-ab58-b5a646314162',
    AutoScalingGroupName: 'ais10-scratch-alpha-processing-apps-cluster-asg',
    LifecycleHookName: 'terminate-hook',
    EC2InstanceId: 'i-0cbb6f82e11dccbb8',
    LifecycleTransition: 'autoscaling:EC2_INSTANCE_TERMINATING'
  }
}
// Simulate the lambda execution for local testing
Index.handler(EventPayload, context);





