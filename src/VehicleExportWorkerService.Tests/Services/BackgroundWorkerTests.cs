using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

namespace VehicleExportWorkerService.Tests.Services
{
    public class BackgroundWorkerTests
    {
        private readonly Mock<ILogger<Worker>> _loggerMock;
        private readonly Mock<IOptions<EmailSettings>> _settingsMock;
        private readonly Mock<IActiveDescVehExport> _activeDescVehExportMock;
        private readonly Mock<IEmailService> _emailServiceMock;
        private readonly Worker _worker;
        private readonly EmailSettings _appSettings;

        public BackgroundWorkerTests()
        {
            _loggerMock = new Mock<ILogger<Worker>>();

            _appSettings = new EmailSettings
            {
                SmtpServer = "test.smtp.com",
                SmtpPort = 587,
                SmtpUser = "testuser",
                SmtpPassword = "testpassword",
                FromEmail = "<EMAIL>",
                ToEmails = new string[] { "<EMAIL>", "<EMAIL>" },
                ActiveDescVehSaveDirectory = Path.Combine(Path.GetTempPath(), "ActiveDescVehExports")
            };

            _settingsMock = new Mock<IOptions<EmailSettings>>();
            _settingsMock.Setup(s => s.Value).Returns(_appSettings);
            
            _activeDescVehExportMock = new Mock<IActiveDescVehExport>();
            _emailServiceMock = new Mock<IEmailService>();
            
            _worker = new Worker(
                _loggerMock.Object,
                _settingsMock.Object,
                _activeDescVehExportMock.Object,
                _emailServiceMock.Object
            );
        }

        [Fact]
        public async Task ExecuteAsync_WhenNoFileCreated_LogsNoFileCreated()
        {
            // Arrange
            _activeDescVehExportMock
                .Setup(a => a.CreateActiveDescVehFileAsync(It.IsAny<string>()))
                .ReturnsAsync(string.Empty);

            // Act
            await _worker.ExecuteAsync(CancellationToken.None);

            // Assert
            _activeDescVehExportMock.Verify(a => a.CreateActiveDescVehFileAsync(_appSettings.ActiveDescVehSaveDirectory), Times.Once);
            _emailServiceMock.Verify(e => e.SendEmailAsync(It.IsAny<string>()), Times.Never);
            VerifyLogMessage("No New Active Vehicle File was created");
            VerifyLogMessage("Active Described Vehicle Export Complete.");
        }

        [Fact]
        public async Task ExecuteAsync_WhenFileCreatedAndEmailSent_LogsEmailSent()
        {
            // Arrange
            const string fileName = "test-file.csv";
            _activeDescVehExportMock
                .Setup(a => a.CreateActiveDescVehFileAsync(It.IsAny<string>()))
                .ReturnsAsync(fileName);
            _emailServiceMock
                .Setup(e => e.SendEmailAsync(fileName))
                .ReturnsAsync(true);

            // Act
            await _worker.ExecuteAsync(CancellationToken.None);

            // Assert
            _activeDescVehExportMock.Verify(a => a.CreateActiveDescVehFileAsync(_appSettings.ActiveDescVehSaveDirectory), Times.Once);
            _emailServiceMock.Verify(e => e.SendEmailAsync(fileName), Times.Once);
            VerifyLogMessage("Active Described Vehicle File Emailed");
            VerifyLogMessage("Active Described Vehicle Export Complete.");
        }

        [Fact]
        public async Task ExecuteAsync_WhenFileCreatedButEmailNotSent_LogsNoEmailSent()
        {
            // Arrange
            const string fileName = "test-file.csv";
            _activeDescVehExportMock
                .Setup(a => a.CreateActiveDescVehFileAsync(It.IsAny<string>()))
                .ReturnsAsync(fileName);
            _emailServiceMock
                .Setup(e => e.SendEmailAsync(fileName))
                .ReturnsAsync(false);

            // Act
            await _worker.ExecuteAsync(CancellationToken.None);

            // Assert
            _activeDescVehExportMock.Verify(a => a.CreateActiveDescVehFileAsync(_appSettings.ActiveDescVehSaveDirectory), Times.Once);
            _emailServiceMock.Verify(e => e.SendEmailAsync(fileName), Times.Once);
            VerifyLogMessage("No Email Sent");
            VerifyLogMessage("Active Described Vehicle Export Complete.");
        }

        [Fact]
        public void Constructor_WithNullParameters_ThrowsArgumentNullException()
        {
            // Assert
            Assert.Throws<ArgumentNullException>(() => new Worker(null!, _settingsMock.Object, _activeDescVehExportMock.Object, _emailServiceMock.Object));
            Assert.Throws<ArgumentNullException>(() => new Worker(_loggerMock.Object, null!, _activeDescVehExportMock.Object, _emailServiceMock.Object));
            Assert.Throws<ArgumentNullException>(() => new Worker(_loggerMock.Object, _settingsMock.Object, null!, _emailServiceMock.Object));
            Assert.Throws<ArgumentNullException>(() => new Worker(_loggerMock.Object, _settingsMock.Object, _activeDescVehExportMock.Object, null!));
        }

        private void VerifyLogMessage(string message)
        {
            _loggerMock.Verify(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }
    }
}
