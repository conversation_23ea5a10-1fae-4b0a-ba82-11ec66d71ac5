using Microsoft.Extensions.Logging;
using Moq;
using VehicleExportWorkerService.Data;
using VehicleExportWorkerService.Models;
using VehicleExportWorkerService.Services;

namespace VehicleExportWorkerService.Tests.Services
{
    public class ActiveDescVehExportTests
    {
        private readonly Mock<ILogger<ActiveDescVehExport>> _loggerMock;
        private readonly Mock<IAisDataDao> _aisDataDaoMock;
        private readonly IActiveDescVehExport _activeDescVehExport;

        public ActiveDescVehExportTests()
        {
            _loggerMock = new Mock<ILogger<ActiveDescVehExport>>();
            _aisDataDaoMock = new Mock<IAisDataDao>();
            _activeDescVehExport = new ActiveDescVehExport(_loggerMock.Object, _aisDataDaoMock.Object);
        }

        [Fact]
        public async Task CreateActiveDescVehFileAsync_WhenNoVehicles_ReturnsEmptyString()
        {
            // Arrange
            _aisDataDaoMock.Setup(dao => dao.GetAllActiveDescVehiclesAsync())
                .ReturnsAsync(new List<ActiveDescVehicle>());

            // Act
            var result = await _activeDescVehExport.CreateActiveDescVehFileAsync("C:\\TestDirectory");

            // Assert
            Assert.Equal(string.Empty, result);
            VerifyLogMessage("No active described vehicles found.");
        }

        [Fact]
        public async Task CreateActiveDescVehFileAsync_WhenVehiclesExist_CreatesCsvFile()
        {
            // Arrange
            var testVehicles = new List<ActiveDescVehicle>
            {
                new ActiveDescVehicle { DescVehicleID = 1, Division = "Division1", Year = 2021, Model = "Model1" },
                new ActiveDescVehicle { DescVehicleID = 2, Division = "Division2", Year = 2022, Model = "Model2" }
            };

            _aisDataDaoMock.Setup(dao => dao.GetAllActiveDescVehiclesAsync())
                .ReturnsAsync(testVehicles);

            var tempDir = Path.Combine(Path.GetTempPath(), Path.GetRandomFileName());
            Directory.CreateDirectory(tempDir);

            try
            {
                // Act
                var result = await _activeDescVehExport.CreateActiveDescVehFileAsync(tempDir);

                // Assert
                Assert.NotEmpty(result);
                Assert.True(File.Exists(result));
            }
            finally
            {
                // Clean up
                if (Directory.Exists(tempDir))
                {
                    Directory.Delete(tempDir, true);
                }
            }
        }

        private void VerifyLogMessage(string message)
        {
            _loggerMock.Verify(
                x => x.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(message)),
                    It.IsAny<Exception>(),
                    It.Is<Func<It.IsAnyType, Exception?, string>>((v, t) => true)),
                Times.Once);
        }
    }
}
