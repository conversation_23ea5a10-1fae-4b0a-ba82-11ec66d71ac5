{"format": 1, "restore": {"/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService.Tests/VehicleExportWorkerService.Tests.csproj": {}}, "projects": {"/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService.Tests/VehicleExportWorkerService.Tests.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService.Tests/VehicleExportWorkerService.Tests.csproj", "projectName": "VehicleExportWorkerService.Tests", "projectPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService.Tests/VehicleExportWorkerService.Tests.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService.Tests/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj": {"projectPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.NET.Test.Sdk": {"target": "Package", "version": "[17.6.3, )"}, "Moq": {"target": "Package", "version": "[4.18.4, )"}, "coverlet.collector": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[6.0.4, )"}, "xunit": {"target": "Package", "version": "[2.4.2, )"}, "xunit.runner.visualstudio": {"target": "Package", "version": "[2.4.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.406/PortableRuntimeIdentifierGraph.json"}}}, "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj", "projectName": "VehicleExportWorkerService", "projectPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/NuGet.Config", "/Users/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"https://api.nuget.org/v3/index.json": {}, "https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AuthoringDataAccessLibrary": {"target": "Package", "version": "[0.1.0-pullrequest0003-0033, )"}, "CsvHelper": {"target": "Package", "version": "[33.1.0, )"}, "Dapper": {"target": "Package", "version": "[2.0.123, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "MySql.Data": {"target": "Package", "version": "[8.1.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/local/share/dotnet/sdk/8.0.406/PortableRuntimeIdentifierGraph.json"}}}}}