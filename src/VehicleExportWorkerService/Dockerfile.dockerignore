##########################################################################
#   Docker Ignore
# 
# Since this dockerignore is not at the build context root, we have to 
# name it according to the Dockerfile name (i.e. <dockerfile-name>.dockerignore).
##########################################################################

# general
.git
.github
.vs	
.cache
**/bin
**/obj
**/*.md
Documentation/

# changes to dockerfiles shouldn't trigger cache misses
**/*Dockerfile
**/*.dockerignore