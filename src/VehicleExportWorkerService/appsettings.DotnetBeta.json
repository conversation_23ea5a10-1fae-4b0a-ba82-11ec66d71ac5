﻿{
  "EmailSettings": {
    "FromEmail": "<EMAIL>",
    "SmtpPassword": "BIaPdzMPB+E8TIsNylMPMEIhkpzzt23A7pTdJFS6Cehu",
    "SmtpPort": 25,
    "SmtpServer": "email-smtp.us-east-1.amazonaws.com",
    "SmtpUser": "AKIAR5IWDGG6HLXK75EQ",
    "ToEmails": [ "<EMAIL>", "<EMAIL>" ],
    "ActiveDescVehSaveDirectory": "C:\\Projects\\described-vehicle-extraction-worker"
  },
  "DbConnectionInfo": {
    "Host": "127.0.0.1",
    "Port": 3306,
    "Database": "digirs_live_dump_us",
    "User": "root",
    "Password": "NewPassword123"
    //"AdditionalOptions": "AllowZeroDateTime=True;ConvertZeroDateTime=True;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console" ],
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "Properties": {
      "Application": "VehicleExportWorkerService"
    }
  }
}