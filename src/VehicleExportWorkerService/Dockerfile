# Use the official .NET 8 SDK image for build
FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Set ARGs and ENVs for private repositories
ARG ARTIFACTORY_USERNAME
ENV ArtifactoryUser=${ARTIFACTORY_USERNAME}

ARG ARTIFACTORY_API_KEY
ENV ArtifactoryPassword=${ARTIFACTORY_API_KEY}

# Copy the project and restore dependencies
COPY ./src/VehicleExportWorkerService/VehicleExportWorkerService.csproj ./VehicleExportWorkerService/
COPY ./src/VehicleExportWorkerService/NuGet.Config ./VehicleExportWorkerService/

WORKDIR /src/VehicleExportWorkerService
RUN dotnet restore --configfile ./NuGet.Config

# Copy everything else and build
COPY ./src/VehicleExportWorkerService/. ./
RUN dotnet build -c Release -o /app/build

# Publish the application
FROM build AS publish
RUN dotnet publish -c Release -o /app/publish

# Use ASP.NET Core Runtime for the final image
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS final
WORKDIR /app

# Copy published files
COPY --from=publish /app/publish .

# Set environment (default to Development)
ARG DOTNET_ENVIRONMENT=Development
ENV DOTNET_ENVIRONMENT=${DOTNET_ENVIRONMENT}
ENV ASPNETCORE_ENVIRONMENT=${DOTNET_ENVIRONMENT}
ENV DOTNET_RUNNING_IN_CONTAINER=true
ENV DOTNET_USE_POLLING_FILE_WATCHER=true

# Expose any ports if needed (uncomment if required)
EXPOSE 80

ENTRYPOINT ["dotnet", "VehicleExportWorkerService.dll"]
