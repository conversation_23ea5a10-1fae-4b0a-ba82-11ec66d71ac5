﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

	<PropertyGroup>
		<OutputType>Exe</OutputType>
		<TargetFramework>net8.0</TargetFramework>
		<Nullable>enable</Nullable>
		<ImplicitUsings>enable</ImplicitUsings>
	</PropertyGroup>

	<ItemGroup>
		<!-- Worker Service Dependencies -->
		<PackageReference Include="AuthoringDataAccessLibrary" Version="1.0.0" />
		<PackageReference Include="CsvHelper" Version="33.1.0" />
		<PackageReference Include="Microsoft.Extensions.Hosting" Version="8.0.0" />
		<PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />

		<!-- Database Access -->
		<PackageReference Include="Dapper" Version="2.0.123" />
		<PackageReference Include="MySql.Data" Version="8.1.0" />
		<PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
		<PackageReference Include="Serilog.Settings.Configuration" Version="9.0.0" />
		<PackageReference Include="Serilog.Sinks.Console" Version="6.0.0" />
		<PackageReference Include="Serilog.Sinks.File" Version="7.0.0" />
	</ItemGroup>
	<ItemGroup>
		<None Update="appsettings*.json">
			<CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
		</None>
		<None Update="appsettings.*.json">
			<DependentUpon>appsettings.json</DependentUpon>
		</None>
	</ItemGroup>

</Project>