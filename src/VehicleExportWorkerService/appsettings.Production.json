﻿{
  "EmailSettings": {
    "FromEmail": "<EMAIL>",
    "SmtpPassword": "BIaPdzMPB+E8TIsNylMPMEIhkpzzt23A7pTdJFS6Cehu",
    "SmtpPort": 25,
    "SmtpServer": "email-smtp.us-east-1.amazonaws.com",
    "SmtpUser": "AKIAR5IWDGG6HLXK75EQ",
    "ToEmails": [ "", "" ],
    "ActiveDescVehSaveDirectory": "C:\\Projects\\described-vehicle-extraction-worker"
  },
  "DbConnectionInfo": {
    "Host": "",
    "Port": 3306,
    "Database": "",
    "User": "",
    "Password": ""
    //"AdditionalOptions": "AllowZeroDateTime=True;ConvertZeroDateTime=True;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console" ],
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "Properties": {
      "Application": "VehicleExportWorkerService"
    }
  }
}