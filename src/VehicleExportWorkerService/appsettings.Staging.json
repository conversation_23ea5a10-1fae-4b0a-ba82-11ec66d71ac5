﻿{
  "EmailSettings": {
    "FromEmail": "<EMAIL>",
    "SmtpPassword": "BIaPdzMPB+E8TIsNylMPMEIhkpzzt23A7pTdJFS6Cehu",
    "SmtpPort": 25,
    "SmtpServer": "email-smtp.us-east-1.amazonaws.com",
    "SmtpUser": "AKIAR5IWDGG6HLXK75EQ",
    "ToEmails": [ "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>" ],
    "ActiveDescVehSaveDirectory": "aisdata/scratch-beta/US/appresource/ActiveDescVeh/"
  },
  "DbConnectionInfo": {
    "Host": "ais10-scratch-beta-incentives-master.ais-internal.com",
    "Port": 3306,
    "Database": "aisdb3_digirs",
    "User": "aisadmin",
    "Password": "lightbomb"
    //"AdditionalOptions": "AllowZeroDateTime=True;ConvertZeroDateTime=True;"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "Serilog": {
    "Using": [ "Serilog.Sinks.Console" ],
    "MinimumLevel": "Information",
    "WriteTo": [
      { "Name": "Console" }
    ],
    "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
    "Properties": {
      "Application": "VehicleExportWorkerService"
    }
  }
}