{"version": 3, "targets": {"net8.0": {}}, "libraries": {}, "projectFileDependencyGroups": {"net8.0": ["AuthoringDataAccessLibrary >= 0.1.0-pullrequest0003-0033", "CsvHelper >= 33.1.0", "Dapper >= 2.0.123", "Microsoft.Extensions.Hosting >= 8.0.0", "Microsoft.Extensions.Logging.Console >= 8.0.0", "MySql.Data >= 8.1.0", "Serilog.AspNetCore >= 9.0.0", "Serilog.Settings.Configuration >= 9.0.0", "Serilog.Sinks.Console >= 6.0.0", "Serilog.Sinks.File >= 7.0.0"]}, "packageFolders": {"/Users/<USER>/.nuget/packages/": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj", "projectName": "VehicleExportWorkerService", "projectPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj", "packagesPath": "/Users/<USER>/.nuget/packages/", "outputPath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/NuGet.Config"], "originalTargetFrameworks": ["net8.0"], "sources": {"/opt/homebrew/Cellar/dotnet/9.0.6/libexec/library-packs": {}, "https://api.nuget.org/v3/index.json": {}, "https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"AuthoringDataAccessLibrary": {"target": "Package", "version": "[0.1.0-pullrequest0003-0033, )"}, "CsvHelper": {"target": "Package", "version": "[33.1.0, )"}, "Dapper": {"target": "Package", "version": "[2.0.123, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "MySql.Data": {"target": "Package", "version": "[8.1.0, )"}, "Serilog.AspNetCore": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Settings.Configuration": {"target": "Package", "version": "[9.0.0, )"}, "Serilog.Sinks.Console": {"target": "Package", "version": "[6.0.0, )"}, "Serilog.Sinks.File": {"target": "Package", "version": "[7.0.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.NETCore.App.Host.osx-arm64", "version": "[8.0.17, 8.0.17]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/opt/homebrew/Cellar/dotnet/9.0.6/libexec/sdk/9.0.107/PortableRuntimeIdentifierGraph.json"}}}, "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget.\n  Response status code does not indicate success: 403.", "libraryId": "AuthoringDataAccessLibrary"}]}