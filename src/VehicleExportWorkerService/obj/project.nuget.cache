{"version": 2, "dgSpecHash": "+yFZLayLJG4=", "success": false, "projectFilePath": "/Users/<USER>/Documents/GitHub/described-vehicle-extraction-worker/src/VehicleExportWorkerService/VehicleExportWorkerService.csproj", "expectedPackageFiles": [], "logs": [{"code": "NU1301", "level": "Error", "message": "Unable to load the service index for source https://artifactory.coxautoinc.com/artifactory/api/nuget/v3/vauto-nuget.\n  Response status code does not indicate success: 403.", "libraryId": "AuthoringDataAccessLibrary"}]}