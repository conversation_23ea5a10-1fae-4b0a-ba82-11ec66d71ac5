{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.Hosting.Lifetime": "Information"}}, "Serilog": {"Using": ["Serilog.Sinks.Console", "Serilog.Sinks.File"], "MinimumLevel": "Debug", "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "C:\\logs\\log-.txt", "rollingInterval": "Day", "shared": true}}], "Enrich": ["FromLogContext", "WithMachineName", "WithThreadId"], "Properties": {"Application": "VehicleExportWorkerService"}}}