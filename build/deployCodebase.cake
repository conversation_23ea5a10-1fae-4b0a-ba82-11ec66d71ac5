#addin "Cake.FileHelpers&version=4.0.1"

#addin "nuget:?package=Newtonsoft.Json&version=11.0.2"
#addin "nuget:?package=AWSSDK.KeyManagementService&version=********"
#addin "nuget:?package=AWSSDK.Core&version=3.7.1"
#addin "nuget:?package=AWSSDK.EC2&version=********"
#addin "nuget:?package=AWSSDK.S3&version=********"
#addin "nuget:?package=AWSSDK.SimpleSystemsManagement&version=********"
#addin "nuget:http://proget/nuget/iss?package=Homenet.MoreCommon"

#load "utils/SsmInstanceMonitor.cake"
#load "utils/ZipUtils.cake"

using System;
using System.IO;
using System.Collections.Generic;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Homenet.MoreCommon;
using Homenet.MoreCommon.Amazon;
using Homenet.MoreCommon.SettingsHelpers;
using Amazon.KeyManagementService;
using Amazon.KeyManagementService.Model;
using Amazon.SimpleSystemsManagement;
using Amazon.SimpleSystemsManagement.Model;
using Amazon.EC2;
using Amazon.EC2.Model;
using Amazon.Runtime;
using Amazon.S3;
using Amazon.S3.Transfer;
using Amazon.RegionEndpoint;


// Constants
const string ApplicationName = "ais10";
const string DefaultRegionName = "us-east-1";
const string ArtifactsFolder = "../artifacts";
const string SolutionRootFolder = "../";
string[] ssmAppStackTypes = new[] { "ConsumerWebstack", "InternalWebstack" };

// Declarations
Dictionary<string,string> alksEnvVars;
SessionAWSCredentials sessionCredentials = null;
AmazonSimpleSystemsManagementClient ssmClient = null;

// Function to return an abbreviated identifier for an AWS region which we use to name the region-specific shared variable files
Func<string,string> GetAbbreviationForRegion = (string regionName) => {
	string[] segments = regionName.Split('-');
	return string.Concat(segments[0][0], segments[1][0], segments[2]); // us-east-1 => ue1
};

// Arguments
// -> Shared
string target = Argument("target", "Default");
string environment = Argument("environment", EnvironmentVariable("environment"));
var hnEnvironment = Homenet.MoreCommon.Settings.GetEnvironment(environment.StartsWith("prod") ? "production-dd" : "development");
string account = Argument("account", "awsaaianp");
string profileName = Argument("profileName", $"{account}:PowerUser");
string regionName = Argument("region", DefaultRegionName);
string ZipPackageName = Argument("packageName", "codebasePackage.zip");
string regionAbbreviation = GetAbbreviationForRegion(regionName);
string activeS3Bucket = hnEnvironment.IsProduction ? $"ais.1-0.application.packages.{regionAbbreviation}" : $"ais.1-0.application.packages.np.{regionAbbreviation}";
string ZipPackagePath = ArtifactsFolder + "/" + ZipPackageName;

// Collect the regions that we support per account (essentially hard-coding them here as we've been doing so far)
var targetRegions = new List<string> { "us-east-1" };
if (hnEnvironment.IsProduction)
	targetRegions.Add("us-west-2");


/// 
/// Grab the session credentials from ALKS 
/// 
Task("Alks")
	.Does(()=>
	{
        Console.WriteLine($"Getting AWS credentials for {profileName} from ALKS\n");
		
        var alksUserName = hnEnvironment.AlksAutoUpdate.ADUsername;
        var alksPassword = hnEnvironment.AlksAutoUpdate.ADPassword.Value;        
        var alks = new AirLiftKeyServices(alksUserName, alksPassword, "awsaaia*:PowerUser", profileName); 
        
		sessionCredentials = new SessionAWSCredentials(alks.DefaultProfile.Key.AccessKey, alks.DefaultProfile.Key.SecretKey, alks.DefaultProfile.Key.SessionToken);       		                
		ssmClient = new AmazonSimpleSystemsManagementClient(sessionCredentials, Amazon.RegionEndpoint.GetBySystemName(regionName)); 
		alksEnvVars = alks.DefaultProfile.Key.AsEnvVars();             
	});

///
/// Zips up the content
///
Task("Package")
    .Does(() => {
		//clean up from previous run
		if (System.IO.Directory.Exists(ArtifactsFolder)){
			System.IO.Directory.Delete(ArtifactsFolder, true);			
		}
		System.IO.Directory.CreateDirectory(ArtifactsFolder);
		
		// Zip up entire root folder
		ZipUtils.ZipWithGitCacheExclusion(SolutionRootFolder, ZipPackagePath);
    });

/// 
/// Uploads the target deployment resource(s) to its appropiate place in S3.
/// 
/// We upload the code package to the appropriate region-specific buckets for all regions that we support under 
/// the target account. In the event of a fail-over, the code will be one less thing to worry over.
/// 
Task("UploadToS3")
	.IsDependentOn("Alks")
	.IsDependentOn("Package")
	.Does(()=>
	{
		Console.WriteLine($"Uploading {ZipPackageName} to region-based buckets for the account\n");

		try
		{
			// Upload the code package to every region-based bucket
			foreach (string region in targetRegions)
			{
				using (var s3client = new AmazonS3Client(sessionCredentials, Amazon.RegionEndpoint.GetBySystemName(region)))
				using (var fileTransferUtility = new TransferUtility(s3client))
				{
					string currentRegionAbbr = GetAbbreviationForRegion(region);
					
					var uploadRequest = new TransferUtilityUploadRequest {
						BucketName = hnEnvironment.IsProduction ? 
									$"ais.1-0.application.packages.{currentRegionAbbr}" : 
									$"ais.1-0.application.packages.np.{currentRegionAbbr}"
					};
					
					uploadRequest.FilePath = ZipPackagePath;
					uploadRequest.Key = $"{environment}/{ZipPackageName}";
					uploadRequest.ContentType = "application/zip";
					fileTransferUtility.Upload(uploadRequest);
				}
			}
		}
		catch (AmazonS3Exception exception)
		{
			Console.WriteLine(exception.Message, exception.InnerException);
		}
	});

/// 
/// Trigger the SSM push to deploy the code from S3 to the webstack servers.
/// 
/// Even though we upload the code to S3 for every region in the account, we only ever have one active region at 
/// a time so we only push the code for the (active) region that was provided as an argument.
/// 
Task("Push")
	.IsDependentOn("Alks")
	.Does(()=>
    {
		var requestsToMonitor = new List<Tuple<string,string>>();
		
		Action<string> PushPackage = (string appStackType) => 
		{
			// Find all the EC2s that SSM will attempt to target
			List<Instance> instances;
			using (var ec2 = new AmazonEC2Client(sessionCredentials, Amazon.RegionEndpoint.GetBySystemName(regionName)))
			{
				var ec2Response = ec2.DescribeInstancesAsync(new DescribeInstancesRequest
				{
					Filters = new List<Filter>
					{
						new Filter("tag:Application", new List<string> { ApplicationName }),
						new Filter("tag:Environment", new List<string> { environment }),
						new Filter("tag:AppStackTypeForSSM", new List<string> { appStackType }),
						new Filter("instance-state-name", new List<string> { "pending", "running", "stopping", "stopped" }), // all but "shutting-down" and "terminated"
					}
				}).Result;
				
				instances = ec2Response.Reservations?.SelectMany(r => r.Instances).ToList() ?? new List<Instance>();
			}
			
			// Gives users a notice, if relevant, that this build will only wait for "running" EC2s to complete their SSM calls
			var notRunningInstances = instances.Where(i => i.State.Name.ToString().ToUpper() != "RUNNING").ToList();
			if (notRunningInstances.Count > 0)
			{
				string nriInstanceIds = string.Join(", ", notRunningInstances.Select(nri => nri.InstanceId).ToArray());
				Console.WriteLine($"Found {notRunningInstances.Count} instances ({nriInstanceIds}) in non-'running' state that will be queued in SSM " +
									"as 'pending' commands to be updated if/when the EC2 comes back online. In the case of an EC2 in 'pending', depending on the timing, " + 
									"it will get updated via its user data script or SSM. In any case, as a result of these special cases, the build script will only wait " +
									"for 'running' EC2s to have their SSM calls completed.");
			}
			
			// Execute the document for the provided set of machines via tagging
			var ssmResponse = ssmClient.SendCommandAsync(new SendCommandRequest
			{
				DocumentName = "GetCodeFromS3",
				Comment = "Deploy code update",
				MaxConcurrency = "33%",
				MaxErrors = "0",
				TimeoutSeconds = 600,
				Parameters = new Dictionary<string, List<string>>
				{
					{ "bucketName", new List<string> { activeS3Bucket } },
					{ "environment", new List<string> { environment } },
					{ "fileName", new List<string> { ZipPackageName } }
				},
				Targets = new List<Target>
				{
					new Target
					{
						Key = "tag:Application",
						Values = new List<string> { ApplicationName }
					},
					new Target
					{
						Key = "tag:Environment",
						Values = new List<string> { environment }
					},
					new Target
					{
						Key = "tag:AppStackTypeForSSM",
						Values = new List<string> { appStackType }
					}
				},
				OutputS3Region = regionName,
				OutputS3BucketName = $"ais.ssm.output.{account}.{regionAbbreviation}",
				OutputS3KeyPrefix = $"{ApplicationName}-{environment}-GetCodeFromS3"
			}).Result;
			
			// Ensure that the response is populated
			Thread.Sleep(10000);
			
			// Give the user a status
			string commandId = ssmResponse.Command.CommandId;
			Console.WriteLine($"Pushing codebase package for {appStackType} to {environment} (CommandId {commandId})");
			
			// Store all the SSM requests for specific instances that we want to ensure finish before this build is considered complete
			string[] runningInstanceIds = instances.Where(i => i.State.Name.ToString().ToUpper() == "RUNNING").Select(ri => ri.InstanceId).ToArray();
			foreach (string runningInstanceId in runningInstanceIds)
				requestsToMonitor.Add(new Tuple<string,string>(commandId, runningInstanceId));
		};
		
		// Push codebase by targeting both webstacks with available machines
		foreach (string appStackType in ssmAppStackTypes) 
			PushPackage(appStackType);
		
		// Start tracking all the SSM calls to individual EC2s and wait for them to finish to report the build status for this run
		SsmInstanceMonitor.StartMonitoringAndWaitToReport(ssmClient, requestsToMonitor);
    });

/// 
/// Removes S3 files from environment folder on S3.
///
/// Ensures that we delete the package from all regions in the account since this is only used for 
/// testing so you'd expect them to be removed as well.
/// 
Task("Destroy")
	.IsDependentOn("Alks")
	.Does(()=>
	{
		// Pulverize the code package for every region-based bucket
		foreach (string region in targetRegions)
		{
			using (var s3client = new AmazonS3Client(sessionCredentials, Amazon.RegionEndpoint.GetBySystemName(region)))
			{
				string currentRegionAbbr = GetAbbreviationForRegion(region);
				
				string bucketName = hnEnvironment.IsProduction ? 
							$"ais.1-0.application.packages.{currentRegionAbbr}" : 
							$"ais.1-0.application.packages.np.{currentRegionAbbr}";
				Console.WriteLine($"Deleting everything (packages, subfolders, etc) with a prefix of \"{environment}/\" from the bucket {bucketName}\n");
				
				var objects = s3client.ListObjectsAsync(bucketName, $"{environment}/").Result;
				foreach (var s3Object in objects.S3Objects)
					s3client.DeleteObjectAsync(bucketName, s3Object.Key).Wait();
			}
		}
	});


// Run the target
RunTarget(target);
