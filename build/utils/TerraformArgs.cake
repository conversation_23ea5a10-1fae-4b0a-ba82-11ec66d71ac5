///<summary>
/// Dictionary of arguments that can easily be converted to a terraform command line argument string
///</summary>
public class TerraformArgs : Dictionary<string, string>
{
    public override string ToString()
    {
        StringBuilder argsStringBuilder = new StringBuilder();
        foreach (var keyValuePair in this) 
        {
            argsStringBuilder.Append(string.Format("-var \"{0}={1}\" ", keyValuePair.Key, keyValuePair.Value));
        }

        return argsStringBuilder.ToString();
    }
}