#addin "nuget:?package=AWSSDK.Core&version=3.7.1"
#addin "nuget:?package=AWSSDK.EC2&version=3.3.54"
#addin "nuget:?package=AWSSDK.SimpleSystemsManagement&version=3.7.4.11"

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Amazon.SimpleSystemsManagement;
using Amazon.SimpleSystemsManagement.Model;

public enum InstanceMonitorResult
{
	Successful,
	Failed,
	Pending,
	InvalidInstance,
	Timeout 
}

public class SsmInstanceMonitor
{
	private readonly AmazonSimpleSystemsManagementClient _client;
	private Thread _updateThread;
	private bool _done;
	private bool _hasStarted;

	public string DocumentName { get; private set; }
	public string CommandId { get; }
	public string InstanceId { get; }
	public int WaitTimeMilliseconds { get; }
	public int MaxAttemptsBeforeTimeout { get; }
	public int Attempts { get; private set; }
	public InstanceMonitorResult? Status { get; private set;  }
	public string FailedStatusDetails { get; private set; }
	public bool IsFinished => Status.HasValue && Status != InstanceMonitorResult.Pending;

	public static void StartMonitoringAndWaitToReport(AmazonSimpleSystemsManagementClient client, List<Tuple<string,string>> commandAndInstanceIdPairs)
	{
		// Fire off monitors for tracking individual SSM requests across as many threads we need
		var monitors = new List<SsmInstanceMonitor>();
		foreach (var pair in commandAndInstanceIdPairs)
		{
			var monitor = new SsmInstanceMonitor(client, pair.Item1, pair.Item2);
			monitor.Start();
			monitors.Add(monitor);
		}

		// Block the main thread and wait until all the documents are complete
		foreach (SsmInstanceMonitor monitor in monitors)
			monitor.Join();
			
		// All monitors have finished now
		Console.WriteLine("All SSM calls have completed for running EC2s in the targeted webstack(s).");
		
		// Notify but don't fail the build if any requests timed out
		var maxAttemptedMonitors = monitors.Where(m => m.Status == InstanceMonitorResult.Timeout).ToList();
		if (maxAttemptedMonitors.Count > 0)
		{
			string maxAttemptedInstanceIds = string.Join(", ", maxAttemptedMonitors.Select(m => m.InstanceId).ToArray());
			Console.WriteLine(
				$"There were {maxAttemptedMonitors.Count}/{monitors.Count} SSM calls that reached the max attempt limit but " + 
				 "could still finish successfully in the near future for Instance Ids: {maxAttemptedInstanceIds}");
		}
		
		// Fail the build if we have failures
		var failedMonitors = monitors.Where(m => m.Status == InstanceMonitorResult.Failed).ToList();
		var missingMonitors = monitors.Where(m => m.Status == InstanceMonitorResult.InvalidInstance).ToList();
		
		if (failedMonitors.Count > 0 || missingMonitors.Count > 0)
		{
			string errorMessage = "";
			
			if (failedMonitors.Count > 0) 
			{
				string failedInstanceIds = string.Join(", ", failedMonitors.Select(m => m.InstanceId).ToArray());
				errorMessage += $"There were {failedMonitors.Count}/{monitors.Count} SSM calls that failed for Instance Ids: {failedInstanceIds}" + System.Environment.NewLine;
			}
			
			if (missingMonitors.Count > 0) 
			{
				string missingInstanceIds = string.Join(", ", missingMonitors.Select(m => m.InstanceId).ToArray());
				errorMessage += $"There were {missingMonitors.Count}/{monitors.Count} SSM calls missing invocations for Instance Ids: {missingInstanceIds}" + System.Environment.NewLine;
			}
			
			throw new Exception(errorMessage);
		}
	}
	
	public SsmInstanceMonitor(AmazonSimpleSystemsManagementClient client, string ssmCommandId, string ec2InstanceId, int waitTimeMilliseconds = 10000, int maxAttemptsBeforeTimeout = 50)
	{
		if (client == null)
			throw new NullReferenceException("AWS SSM Client is null");

		CommandId = ssmCommandId;
		InstanceId = ec2InstanceId;
		WaitTimeMilliseconds = waitTimeMilliseconds;
		MaxAttemptsBeforeTimeout = maxAttemptsBeforeTimeout;

		_client = client;
	}

	public void Join() => _updateThread.Join();

	public void Start()
	{
		if (_hasStarted) return;

		_hasStarted = true;
		_done = false;
		_updateThread = new Thread(Main);
		_updateThread.Start();
	}

	protected TimeSpan CheckStatus()
	{
		DateTime started = DateTime.Now;

		Console.WriteLine($"Checking status of CommandId {CommandId} and InstanceId {InstanceId}");
		try
		{
			var response = _client.GetCommandInvocationAsync(new GetCommandInvocationRequest
			{
				InstanceId = InstanceId,
				CommandId = CommandId,
			}).Result;

			CommandInvocationStatus status = response.Status;
			DocumentName = response.DocumentName;
			Console.WriteLine($"{DocumentName} document for CommandId {CommandId} and InstanceId {InstanceId} has reported a status of {status}");

			if (status == CommandInvocationStatus.Cancelled || status == CommandInvocationStatus.Failed || status == CommandInvocationStatus.TimedOut)
				Status = InstanceMonitorResult.Failed;
			else if (status == CommandInvocationStatus.Success)
				Status = InstanceMonitorResult.Successful;
			else 
				Status = InstanceMonitorResult.Pending;

			if (Status == InstanceMonitorResult.Failed)
				FailedStatusDetails = response.Status.ToString();
		}
		catch (InvocationDoesNotExistException)
		{
			Status = InstanceMonitorResult.InvalidInstance;
		}
		catch (Exception ex)
		{ 
			Console.WriteLine($"Exception occurred checking status of CommandId {CommandId} and InstanceId {InstanceId}: {ex.Message}");
		}

		return DateTime.Now - started;
	}

	protected void Main()
	{
		while (!_done)
		{
			Attempts++;
			if (Attempts > MaxAttemptsBeforeTimeout)
			{
				Console.WriteLine(
					$"Reached max attempt limit while checking status of CommandId {CommandId} and InstanceId {InstanceId}. " + 
					 "The SSM call could still finish but it's taking too long so we can't hold up the build forever.");
					 
				Status = InstanceMonitorResult.Timeout;
				_done = true;
				continue;
			}
			
			TimeSpan workTime = CheckStatus();

			if (IsFinished)
			{
				_done = true;
				continue;
			}

			int sleepFor = WaitTimeMilliseconds - Convert.ToInt32(Math.Ceiling(workTime.TotalMilliseconds));
			if (sleepFor > 0)
				Thread.Sleep(sleepFor);
		}
	}
}
