﻿using Cake.Core.IO;
using Cake.Core;
using Cake.Core.Annotations;
using Cake.Common.Diagnostics;
using System;
using System.IO;
using System.IO.Compression;
using System.IO.Path;
using System.Collections.Generic;

///<summary>
/// Helper methods that perform tasks related to .zip files
///</summary>
///
///<remarks> 
/// This class assumes that the build script is run from "/build" for relative paths to work correctly
/// Include #load "utils/ZipUtils.cake" at the top of a file to use these helper methods
///</remarks>

public static class ZipUtils
{
	// Path of the temporary directory, which is deleted after the zip file is created
	private const string TempDirectoryPathBase = @"C:\applications\cake-zip-utils\";

	///<summary>
	/// Helper method to zip a directory and its subdirectories excluding the ".git/" directory
	///</summary>
	///
	///<param name="sourceDirName">Path of directory to zip up</param>
	///<param name="destZipFilePath">optional name for .zip file</param>
	/// 
	///<remarks> 
	///	Zip file will be created in the same directory that contains <paramref name="sourceDirName"/>
	/// A temporary directory is as an intermediate step and deleted at the end
	///</remarks>
	///
	///<example> 
	///<code>
	/// #load "utils/ZipUtils.cake"
	/// 
	/// Task("zip")
	/// .Does(() => {
	/// 	ZipUtils.ZipWithGitCacheExclusion( @".\code");
	/// });	
	/// RunTarget(target);
	///</code>
	///</example>

	public static void ZipWithGitCacheExclusion(string sourceDirName, string destZipFilePath = null)
	{
		ZipWithDirectoryExclusions(sourceDirName, new HashSet<string> { ".git" }, destZipFilePath);
	}

	///<summary>
	/// Helper method to zip a directory with one or more user-specified subdirectory exclusions
	///</summary>
	///
	///<param name="sourceDirName">Path of directory to zip up</param>
	///<param name="exclusions">A HashSet of subdirectories to exclude if present</param>
	///<param name="destZipFilePath">optional name for .zip file</param>
	/// 
	///<remarks> 
	///	Zip file will be created in the same directory that contains <paramref name="sourceDirName"/>
	/// A temporary directory is as an intermediate step and deleted at the end
	///</remarks>
	///
	///<example> 
	///<code>
	/// #load "utils/ZipUtils.cake"
	/// 
	/// Task("zip")
	/// .Does(() => {
	///		HashSet<string> exclusions = new HashSet<string>();
	///
	///		exclusions.Add(".git");
	///		exclusions.Add("excludeTest1");
	///		exclusions.Add("excludeTest2");
	///	
	///		ZipUtils.ZipWithDirectoryExclusions( @".\code", exclusions;
	/// });	
	/// RunTarget(target);
	///</code>

	public static void ZipWithDirectoryExclusions(string sourceDirName, HashSet<string> exclusions, string destZipFilePath = null)
	{
		// If no destination for the zip file was provided, use default
		if (destZipFilePath == null)
			destZipFilePath = System.IO.Path.GetFullPath(sourceDirName) + ".zip";

		// Declare for scoping
		DirectoryInfo tempDirectory = null;

		try 
		{
			// Create temp directory
			tempDirectory = GetTempDirectory("zip-with-exclusions-temp");

			// Make a temporary copy of the specified directory with the specified exclusions
			DirectoryCopy(sourceDirName, tempDirectory.FullName, exclusions);

			// Delete .zip if it already exists
			if (System.IO.File.Exists(destZipFilePath))
				System.IO.File.Delete(destZipFilePath);

			// Create .zip
			System.IO.Compression.ZipFile.CreateFromDirectory(tempDirectory.FullName, destZipFilePath);
		}
		catch (Exception e)
		{
			Console.WriteLine("Error when calling ZipWithDirectoryExclusions: " + e.Message);
		}
		finally
		{
			// Delete temp dir
			System.IO.Directory.Delete(tempDirectory.FullName, true);
		}
	}

	///<summary>
	/// Helper method to zip a specified directory, while ignoring any subdirectories
	///</summary>
	///
	/// <param name="sourceDirName">Path of directory to zip up</param>
	/// 
	///<remarks> 
	///	Zip file will be created in the same directory that contains <paramref name="sourceDirName"/>
	/// A temporary directory is as an intermediate step and deleted at the end
	///</remarks>
	///
	///<example> 
	///<code>
	/// #load "utils/ZipUtils.cake"
	/// 
	/// Task("zip")
	/// .Does(() => {
	/// 	ZipUtils.ZipOnlyFiles( @".\code");
	/// });	
	/// RunTarget(target);
	///</code>
	
	public static void ZipOnlyFiles(string sourceDirName)
	{
		// Create path strings for the temp dir and the new zip folder
		string destDirName = sourceDirName + "-copy";
		string destZipFilePath = sourceDirName + ".zip";

		// Check if source dir exists
		DirectoryInfo dir = new DirectoryInfo(sourceDirName);

		if (!dir.Exists)
		{
			throw new DirectoryNotFoundException(
				"Source directory does not exist or could not be found: "
				+ sourceDirName);
		}

		// If the destination directory doesn't exist, create it.
		if (!System.IO.Directory.Exists(destDirName))
		{
			System.IO.Directory.CreateDirectory(destDirName);
		}

		// Get the files in the directory and copy them to the temporary directory.
		FileInfo[] files = dir.GetFiles();
		foreach (FileInfo file in files)
		{
			string temppath = System.IO.Path.Combine(destDirName, file.Name);
			file.CopyTo(temppath, false);
		}

		// Delete .zip if it already exists
		if (System.IO.File.Exists(destZipFilePath))
			System.IO.File.Delete(destZipFilePath);

		// Create .zip
		System.IO.Compression.ZipFile.CreateFromDirectory(destDirName, destZipFilePath);

		// Delete temp dir
		System.IO.Directory.Delete(destDirName, true);

	}

	///<summary>
	/// Create temporary directory with path "{TempDirectoryPathBase}{tempDirectory}"
	///</summary>
	///
	///<param name="tempDirectory">Name of the temp directory to append to the end of TempDirectoryPathBase</param>
	///
	///<returns>DirectoryInfo object for the new temporary directory</return>

	private static DirectoryInfo GetTempDirectory(string tempDirectory)
	{
		// Concatenate both parts of the full path of the temp directory to be created
		string tempPath = System.IO.Path.Combine(TempDirectoryPathBase, tempDirectory);

		// Create temp directory at the specified path
		var newTempDir = System.IO.Directory.CreateDirectory(tempPath);
		
		return newTempDir;
	}

	///<summary>
	/// Copy all subfolders and files of a source directory into a destination directory
	///</summary>
	///
	///<param name="sourceDirName">Path of directory to copy</param>
	///<param name="destDirName">Path of destination directory to store copy of source directory</param>
	///
	///</param name="exclusions">HashSet of subdirectories to exclude if present</param>

	private static void DirectoryCopy(string sourceDirName, string destDirName, HashSet<string> exclusions)
	{
		// Get the subdirectories for the specified directory.
		DirectoryInfo dir = new DirectoryInfo(sourceDirName);

		if (!dir.Exists)
		{
			throw new DirectoryNotFoundException(
				"Source directory does not exist or could not be found: "
				+ sourceDirName);
		}

		DirectoryInfo[] dirs = dir.GetDirectories();

		System.IO.Directory.CreateDirectory(destDirName);

		// Get the files in the directory and copy them to the temporary directory.
		FileInfo[] files = dir.GetFiles();
		foreach (FileInfo file in files)
		{
			string tempPath = System.IO.Path.Combine(destDirName, file.Name);
			file.CopyTo(tempPath, true);
		}

		// Copy them and their contents to the temporary directory.
		foreach (DirectoryInfo subdir in dirs)
		{
			string temppath = System.IO.Path.Combine(destDirName, subdir.Name);
				
			// If the subdirectory is an exclusion, skip over it
			// Otherwise, call recursively to ensure all of the subdir's contents are copied to temp dir
			if (!(exclusions.Contains(subdir.Name)))
			{
				DirectoryCopy(subdir.FullName, temppath, exclusions);
			}
		}
	}
}
