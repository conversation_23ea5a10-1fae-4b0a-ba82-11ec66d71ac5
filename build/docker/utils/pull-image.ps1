# Pull Docker Images from AWS ECR Repository
# 
# Dependencies:
# Docker, AWS CLI, AWS Powershell Tools

param (
	[Parameter(Mandatory=$true)]
	[string]$RepositoryName,
	
	[Parameter(Mandatory=$false)]
	[string]$ImageTagName = "latest",
	
	[Parameter(Mandatory=$true)]
	[string]$AwsAccessKey,
	
	[Parameter(Mandatory=$true)]
	[string]$AwsSecretKey,
	
	[Parameter(Mandatory=$true)]
	[string]$AwsSessionToken,
	
	[Parameter(Mandatory=$false)]
	[string]$AwsRegion = "us-east-1",
	
	[switch]$Quiet = $false
)

# Set strict mode
Set-StrictMode -Version Latest

# Setup our AWS session
Set-AWSCredentials -AccessKey $AwsAccessKey -SecretKey $AwsSecretKey -SessionToken $AwsSessionToken

# Get our account number and let it fail if above credentials are incorrect
$accountId = ""
Try
{
	"Fetching Account Id" | Write-Host
	$identity = Get-STSCallerIdentity
	$accountId = $identity.Account
}
Catch
{
	"Incorrect AWS Credentials" | Write-Host
	Exit 1
}

# Set environment variables for AWS CLI to be used instead of the credential file
$env:AWS_ACCESS_KEY_ID = $AwsAccessKey
$env:AWS_SECRET_ACCESS_KEY = $AwsSecretKey
$env:AWS_SESSION_TOKEN = $AwsSessionToken

# Get docker login command for pushing to the repo
$dockerLogin = aws ecr get-login --no-include-email --region $AwsRegion
if ($LASTEXITCODE -ne 0) {
	"Failed fetching Docker login command for repository" | Write-Host
	Exit 1
}

# Pull the image from the repo
Invoke-Expression -Command $dockerLogin
Invoke-Expression "& docker pull ${accountId}.dkr.ecr.${AwsRegion}.amazonaws.com/${RepositoryName}:${ImageTagName}" | Out-Host

# If quiet mode isn't set, wait for user to hit any key to exit
if (-not($Quiet)) { 
	"Press any key to continue..." | Write-Host
	$pause = $host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown") 
}
