# AWS ECR Docker Repository
PowerShell scripts to help manage the deployment and pulling of images to an AWS ECR Docker Repository. 


## Utils

These scripts, particularly referring to the one that deploys an image, aren't meant to be invoked directly for deploying images to ECS for official deployments - but rather, aside from testing, they are meant to be invoked by other scripts which are specifically named after the images that they build. These utils scripts only handle one image within a single account for a particular region at a time.

**Build/Deploy an Image**   

Docker images can be deployed using the script deploy-image.bat. The DockerFile needs to exist in a subdirectory of the solution (as shown with the MongoDB 
sample included in this repo) which is the expected value for the -RepositoryName argument. There's also a -AwsRegion argument if there's any reason to vary 
or change the default region.

```./deploy-image.bat -RepositoryName mongodb -ImageTagName latest -AwsAccessKey myaccesskey -AwsSecretKey mysecretkey -AwsSessionToken mysessiontoken```


**Pull an Image to Run**  

Docker images can be pulled down from the ECR Repository to be run using the script pull-image.bat.

```./pull-image.bat -RepositoryName mongodb -ImageTagName latest -AwsAccessKey myaccesskey -AwsSecretKey mysecretkey -AwsSessionToken mysessiontoken```


## Deployments

For each Docker image that we host in ECS, there should be a bat and ps1 script to handle its deployment which in turn call the deployment script in utils/ under the hood. Since the underlying script in utils is designed to only deploy a single image to one region for a particular account, this additional layer of scripts per image handles it once per supported region within a provided account. We've decided as a rule to always deploy image updates to all regions for an account to be prepared in case of region failure (similar to how we do AMIs).

**Example Deployment of All Regions to Production Account**   

```./deploy-processing-task-base.bat -IsProduction -AwsAccessKey myaccesskey -AwsSecretKey mysecretkey -AwsSessionToken mysessiontoken``` 

**Example Deployment of All Regions to Non-Production Account**  

```./deploy-processing-task-base.bat -AwsAccessKey myaccesskey -AwsSecretKey mysecretkey -AwsSessionToken mysessiontoken``` 
