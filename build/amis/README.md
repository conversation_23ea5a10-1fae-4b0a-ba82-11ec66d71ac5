# AWS AMI Build/Deployment Documentation

## Overview    

**Terraform**   
There is a Terraform module at infrastructure/modules/ami/ which can create any AMI based on a Base AMI ID and a User Data Script. Within that module directory, there's a subdirectory called user-data which contains additional subdirectories named after the AMIs that they represent. These further subdirectories then contain its user data script named as script.cloudinit. The Terraform variable to specify the user data script actually uses the name of that subdirectory (\*\*) in infrastructure/modules/ami/user-data/\*\*/.

**Cake**   
The Cake script to build/deploy the AMI by invoking the Terraform module is located at build/amis/deployAmi.cake. This build script is similar to our generic Cake script for deploying Terraform components; however, there main differences (aside from being specific to handling AMIs) are a) remote tfstate isn't used, and similarly b) no destroy target. This script also expects, as arguments, the same required variables for the Terraform module: Base AMI ID and a User Data script directory name. It's designed to only deploy a single AMI at a time to a particular account and region combination per execution.

**PowerShell**   
The Cake script to deploy an AMI to a particular account/region combination is invoked by the PowerShell script at build/amis/deployAmi.ps1. This PowerShell script also accepts the same argument for the User Data script directory name, along with a flag for whether or not it's being deployed to the production account. Since we decided to deploy AMIs to every supported region within an account at the same time (instead of waiting for a failover occasion), this script handles invoking Cake once per region for the implicitly specified account based on that flag. It's worth noting that this script is still AMI agnostic.

**Batch**    
The batch script(s) are what we actually call to trigger a build/deployment of an AMI to all the regions of an account. The batch scripts are also located in build/amis/ where there's one specifically for every AMI that we manage (i.e. build/amis/deployAmiForConsumerWebstack.bat). This is the location where we actually provide the values of the Base AMI ID and User Data script directory name which uniquely identifies a managed AMI. 

As an example, the build/amis/deployAmiForWebstack.bat invokes: 
```
powershell.exe -ExecutionPolicy Bypass -File deployAmi.ps1 -AmiUserDataFolderName consumer-webstack %*
```

By default, the above example would deploy to the non-production account, but using the mentioned flag we can deploy to both production regions by executing: 
```
cd build/amis/
./deployAmiForWebstacks.bat -IsProduction
```


## Deploying an Existing AMI

As stated above, you run the bat file for the appropriate AMI to trigger its deployment for all supported regions in the provided account (determined by -IsProduction flag); however, due to the fact 
that the Terraform scripts make AWS CLI calls, you must update your credentials file under the %homepath%\.aws directory to match the account that you're targeting. Unfortunuately this is the only way 
that we could get it working at the moment but it simply means that you must again update the credentials file before running the bat file to deploy to the other account. It may be worth looking into 
passing the access keys from the Cake script into the CLI calls to take away this pain point.


## Creating a New AMI

If you need to create a new AMI, follow the pattern/framework that's been established using the following steps: 
    
1. Create a new subdirectory under infrastructure/modules/ami/user-data/ named after the AMI
2. Create the user data script called script.cloudinit in the newly created subdirectory
3. Copy and paste a bat script (i.e. deployAmiForWebstack.bat) as a template and name the copy following suit
4. Edit the newly created bat script and update AmiUserDataFolderName (name of the subdirectory from step one)

Update:   
Also need to create a new deployAmi.ps1 at the moment since I was recently forced to move and hard-code the Base AMI IDs in there since they vary per region. To avoid more copy-pasta files though, the next time that we need a new managed AMI, we should update deployAmi.ps1 to accept a dictionary of region => amiId to keep it generic (see TODO note in script).
