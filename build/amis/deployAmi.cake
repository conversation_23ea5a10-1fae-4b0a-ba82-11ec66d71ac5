#addin "nuget:?package=Newtonsoft.Json&version=11.0.2"
#addin "nuget:?package=AWSSDK.KeyManagementService&version=3.3.5.9"
#addin "nuget:?package=AWSSDK.Core&version=3.3.24.3"
#addin "nuget:http://proget/nuget/iss?package=Homenet.MoreCommon"

#load "../utils/TerraformArgs.cake"

using System.IO;
using System.Linq;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Homenet.MoreCommon;
using Homenet.MoreCommon.Amazon;
using Homenet.MoreCommon.SettingsHelpers;
using Amazon.Runtime;


// Definitions
const string TerraformVersion = "0.13.7";
const string DefaultRegionName = "us-east-1";

// Declarations
Dictionary<string,string> alksEnvVars;
Dictionary<string,string> terraformArguments;
string terraformExePath = string.Empty;

// Exit code handling
int lastExitCode = 0;
Action<int> VerifyExitCode = (int exitCode) => {
	if (exitCode != 0) throw new Exception($"Task exited with an exit code of {exitCode}");
};

// Returns an abbreviated identifier for an AWS region which we use to name the region-specific shared variable files
Func<string,string> GetAbbreviationForRegion = (string regionName) => {
	string[] segments = regionName.Split('-');
	return string.Concat(segments[0][0], segments[1][0], segments[2]); // us-east-1 => ue1
};

// Arguments
// -> Cake
var target = Argument("target", "Default");
// -> Terraform
var account = Argument("account", EnvironmentVariable("ACCOUNT") ?? "awsaaianp");
var profileName = Argument("profileName", $"{account}:PowerUser");
var regionName = Argument("region", EnvironmentVariable("REGION") ?? DefaultRegionName);
// -> Global
var environment = Argument("environment", EnvironmentVariable("ENVIRONMENT") ?? "non-prod");
var buildNumber = Argument("buildNumber", EnvironmentVariable("BUILD_NUMBER") ?? DateTime.Now.ToString("yyyyMMddHHmmss"));
var launchedBy = Argument("launchedBy", EnvironmentVariable("USERNAME") ?? "me");
var launchedOn = Argument("launchedOn", DateTime.Now.ToString("yyyy-MM-ddTHH:mm:ssZ"));
// -> Custom
var amiUserDataFolderName = Argument("amiUserDataFolderName", EnvironmentVariable("AMI_USERDATA_FOLDER_NAME"));
var amiBaseImageId = Argument("amiBaseImageId", EnvironmentVariable("AMI_BASE_IMAGE_ID"));

// Misc
var hnEnvironment = Homenet.MoreCommon.Settings.GetEnvironment(environment.StartsWith("production") ? "production-dd" : "development");
var tfAmiRootModuleDirectory = new DirectoryPath($"../../infrastructure/environments/base/ami/");
var tfAmiUserDataScriptFile = new FilePath($"../../infrastructure/modules/ami/user-data/{amiUserDataFolderName}/script.cloudinit");
string accountType = hnEnvironment.IsProduction ? "prod" : "nonprod";
string regionAbbreviation = GetAbbreviationForRegion(regionName);
var sharedVariableAccountFile = new FilePath($"../../infrastructure/shared-variables.{accountType}.tf");
var sharedVariableRegionFile = new FilePath($"../../infrastructure/shared-variables.{accountType}.{regionAbbreviation}.tf");
string tfstateFilePath = tfAmiRootModuleDirectory.FullPath + "/terraform.tfstate";

/// 
/// Verify safety checks to ensure we have all the inputs needed to build
/// 
Task("SafetyCheck")
	.Does(()=>
	{
        Console.WriteLine($"Performing safety checks");
		
		// Verify arguments that are required but don't have any defaults available (via hard-coded nor environment variable)
        if (string.IsNullOrEmpty(environment))
			throw new Exception("Must specify an environment name for the AMI being deployed Terraform (i.e. production or non-prod)");
        if (string.IsNullOrEmpty(regionName))
			throw new Exception("Must specify a region name for the AMI to be deployed with Terraform");
		
		// Verify the region is a real thing
		if (Amazon.RegionEndpoint.GetBySystemName(regionName).ToString().StartsWith("Unknown"))
			throw new Exception($"Invalid region specified as {regionName}");
		
		// Verify the root module directory exists
		if (!DirectoryExists(tfAmiRootModuleDirectory))
			throw new Exception($"Can't find Terraform root module path at {tfAmiRootModuleDirectory.FullPath}");
		
		// Verify the shared variable files that are needed exist
		if (!FileExists(sharedVariableAccountFile))
			throw new Exception($"Unable to find shared variable file for account at {sharedVariableAccountFile.FullPath}");
		if (!FileExists(sharedVariableRegionFile))
			throw new Exception($"Unable to find shared variable file for region at {sharedVariableRegionFile.FullPath}");
		
		// Verify custom arguments for AMIs, including confirming the user data script is found based on convention
		if (string.IsNullOrEmpty(amiBaseImageId))
			throw new Exception("Must provide the argument `amiBaseImageId` which is the Id of the AMI to use as the starting base");
		if (string.IsNullOrEmpty(amiUserDataFolderName))
			throw new Exception("Must provide the argument `amiUserDataFolderName` which matches to the Terraform root module directory and its user data script");
		if (!FileExists(tfAmiUserDataScriptFile))
			throw new Exception($"Can't find user data script at {tfAmiUserDataScriptFile.FullPath}");
	});

/// 
/// Grab the session credentials from ALKS so we can perform terraform deployments
/// 
Task("Alks")
	.Does(()=>
	{
        Console.WriteLine($"Getting AWS Credentials for {profileName} from ALKS");
		
        var alks = new AirLiftKeyServices(hnEnvironment.AlksAutoUpdate.ADUsername, hnEnvironment.AlksAutoUpdate.ADPassword.Value, "awsaaia*:PowerUser", profileName);				        
		alksEnvVars = alks.DefaultProfile.Key.AsEnvVars();
	});

/// 
/// Ensure that we have the correct version of terraform
/// 
Task("TerraformVersion")
	.Does(()=>
    {
        Console.WriteLine("Verifying Terraform installation for correct version with Chocolatey");
		
		string chocoPath = System.Environment.GetEnvironmentVariable("ChocolateyInstall");
		terraformExePath = System.IO.Path.Combine(chocoPath, "lib", $"terraform.{TerraformVersion}", "tools");
		
		if (!System.IO.Directory.Exists(terraformExePath))
		{
			// Must specify multi-version flag as well as the force flag to make sure it'll reinstall with a versioned folder name if already installed
			lastExitCode = StartProcess("choco", new ProcessSettings {
				Arguments = $"install terraform --version {TerraformVersion} -my --force",
			});
			
			VerifyExitCode(lastExitCode);
		}
    });
	
/// 
/// Build terraform arguments for the build
/// 
Task("TerraformArgs")
    .Does(() => 
    {        
        Console.WriteLine("Setting terraform arguments");
		
        terraformArguments = new TerraformArgs 
        {
            { "region", regionName },
            { "build_number", buildNumber },
            { "environment", accountType },
            { "component", "amis" },
            { "launched_by", launchedBy },
            { "launched_on", launchedOn },
            { "slack_contact", hnEnvironment.AISAuthoring.AWSNotificationSlackChannel.Replace("#", "+") }, // use valid tags for monkey
			{ "base_ami_id", amiBaseImageId },
			{ "user_data_folder_name", amiUserDataFolderName },
        };
    });

/// 
/// Initialize the deployment (merge shared vars, download modules, and setup s3 state)
/// 
Task("InitDeployment")
    .IsDependentOn("SafetyCheck")
    .IsDependentOn("TerraformVersion")
	.IsDependentOn("Alks")
    .IsDependentOn("TerraformArgs")
	.Does(()=>
    {
		var sharedVariableGlobalFile = new FilePath("../../infrastructure/shared-variables.global.tf");
		var dataSourceGlobalFile = new FilePath("../../infrastructure/data-sources.global.tf");
		
		// Copy global shared variable tf file into the environment's component root module
		CopyFile(sharedVariableGlobalFile.FullPath, System.IO.Path.Combine(tfAmiRootModuleDirectory.FullPath, "shared-variables-global.tf"));

		// Copy the data sources tf file into the environment's component root module 
		CopyFile(dataSourceGlobalFile.FullPath, System.IO.Path.Combine(tfAmiRootModuleDirectory.FullPath, "shared-data-sources.tf"));

		// Copy account-specific shared variable tf file into the environment's component root module
		CopyFile(sharedVariableAccountFile.FullPath, System.IO.Path.Combine(tfAmiRootModuleDirectory.FullPath, "shared-variables-account.tf"));

		// Copy the region-specific shared variable tf file for the account into the environment's component root module 
		CopyFile(sharedVariableRegionFile.FullPath, System.IO.Path.Combine(tfAmiRootModuleDirectory.FullPath, "shared-variables-region.tf"));

		// Display version so we have record of it in the build logs just in case
		lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings { Arguments = "--version" });
		VerifyExitCode(lastExitCode);
		
		// Initialize terraform (intentionally no remote state)
		lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = $"init -input=false -get=true",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfAmiRootModuleDirectory
        });
		VerifyExitCode(lastExitCode);
    });

/// 
/// Run terraform plan to ensure the environment declaration is sane and creates a legit plan to apply
/// 
Task("Plan")
	.IsDependentOn("InitDeployment")
	.Does(()=>
    {
		// Remove tfstate file so any consecutive runs don't fail (i.e. deploying production images to both regions)
		if (FileExists(tfstateFilePath)) 
			DeleteFile(tfstateFilePath); 

		// Plan it
        lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = $"plan {terraformArguments} -out=plan",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfAmiRootModuleDirectory
        });   
		VerifyExitCode(lastExitCode);
    });

/// 
/// Apply the plan (i.e. build the AMI)
/// 
Task("Apply")
    .IsDependentOn("Plan")
	.Does(()=>
    {
		// Remove tfstate file so any consecutive runs don't fail (i.e. deploying production images to both regions)
		if (FileExists(tfstateFilePath)) 
			DeleteFile(tfstateFilePath); 
		
		// Apply the plan to create the AMI
        lastExitCode = StartProcess(System.IO.Path.Combine(terraformExePath, "terraform.exe"), new ProcessSettings {
            Arguments = "apply plan",
            EnvironmentVariables = alksEnvVars,
            WorkingDirectory = tfAmiRootModuleDirectory
        });
		VerifyExitCode(lastExitCode);
    });
	
/// 
/// Default task is run if no task is specified
/// 
Task("Default")
	.IsDependentOn("Plan")
	.Does(() => {});


// Run the provided target
RunTarget(target);
