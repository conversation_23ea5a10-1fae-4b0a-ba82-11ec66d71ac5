name: Deploy ECS Task Definitions

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (nonprod, prod)'
        required: true
        default: 'nonprod'
        type: choice
        options:
          - nonprod
          - prod
      region:
        description: 'AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      task_name:
        description: 'Task definition name'
        required: true
        type: string
      docker_image_uri:
        description: 'Docker image URI (ECR)'
        required: false
        type: string
      component_id:
        description: 'Component ID for tagging'
        required: true
        type: string
      terraform_action:
        description: 'Terraform action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      auto_approve:
        description: 'Auto-approve Terraform apply (use with caution)'
        required: false
        default: false
        type: boolean

  push:
    branches:
      - main
      - develop
    paths:
      - 'separated-infrastructure/task-deployment/**'
      - '.github/workflows/deploy-task-definitions.yml'
  pull_request:
    branches:
      - main
      - develop
    paths:
      - 'separated-infrastructure/task-deployment/**'
      - '.github/workflows/deploy-task-definitions.yml'

env:
  TF_VERSION: '1.5.7'
  AWS_DEFAULT_REGION: ${{ inputs.region || 'us-east-1' }}
  ENVIRONMENT: ${{ inputs.environment || 'nonprod' }}

jobs:
  validate:
    name: Validate Terraform Configuration
    runs-on: ubuntu-latest
    outputs:
      tf-fmt-check: ${{ steps.fmt.outputs.exitcode }}
      tf-validate-check: ${{ steps.validate.outputs.exitcode }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Terraform Format Check
        id: fmt
        run: |
          cd separated-infrastructure/task-deployment
          terraform fmt -check=true -diff=true
        continue-on-error: true

      - name: Terraform Init
        run: |
          cd separated-infrastructure/task-deployment
          terraform init -backend=false

      - name: Terraform Validate
        id: validate
        run: |
          cd separated-infrastructure/task-deployment
          terraform validate

      - name: Validation Summary
        run: |
          echo "## Terraform Validation Results" >> $GITHUB_STEP_SUMMARY
          echo "| Check | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|-------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Format | ${{ steps.fmt.outcome == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Validate | ${{ steps.validate.outcome == 'success' && '✅ Passed' || '❌ Failed' }} |" >> $GITHUB_STEP_SUMMARY

  security-scan:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: validate
    if: always()
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Run Checkov Security Scan
        uses: bridgecrewio/checkov-action@master
        with:
          directory: separated-infrastructure/task-deployment
          framework: terraform
          output_format: sarif
          output_file_path: checkov-results.sarif
          soft_fail: true

      - name: Upload Checkov Results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: checkov-results.sarif

  plan:
    name: Terraform Plan
    runs-on: ubuntu-latest
    needs: [validate, security-scan]
    if: always() && needs.validate.result == 'success'
    environment: ${{ inputs.environment || 'nonprod' }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Set Terraform Variables
        run: |
          cd separated-infrastructure/task-deployment
          # Variables are now resolved internally by the module
          # Only pass required input variables
          echo "TF_VAR_environment=${{ env.ENVIRONMENT }}" >> $GITHUB_ENV
          echo "TF_VAR_region=${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "TF_VAR_component=processing-apps" >> $GITHUB_ENV
          echo "TF_VAR_component_id=${{ inputs.component_id }}" >> $GITHUB_ENV
          echo "TF_VAR_build_number=${{ github.run_number }}" >> $GITHUB_ENV
          echo "TF_VAR_launched_by=github-actions" >> $GITHUB_ENV
          echo "TF_VAR_launched_on=$(date -u +%Y-%m-%d)" >> $GITHUB_ENV

          # Cluster Configuration
          echo "TF_VAR_use_remote_state=true" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_bucket=${{ vars.TF_STATE_BUCKET }}" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" >> $GITHUB_ENV

          # Task Configuration
          echo "TF_VAR_task_friendly_name=${{ inputs.task_name }}" >> $GITHUB_ENV
          echo "TF_VAR_container_definition_path=./container-definitions/${{ inputs.task_name }}.json" >> $GITHUB_ENV
          echo "TF_VAR_image_url_name_tag=${{ inputs.docker_image_uri || vars.DEFAULT_DOCKER_IMAGE }}" >> $GITHUB_ENV

          # IAM Configuration
          echo "TF_VAR_task_role_arn=${{ vars.TASK_ROLE_ARN }}" >> $GITHUB_ENV
          echo "TF_VAR_execution_role_arn=${{ vars.EXECUTION_ROLE_ARN }}" >> $GITHUB_ENV

          # Optional Configuration (only set if provided)
          if [ -n "${{ vars.INI_BUCKET }}" ]; then
            echo "TF_VAR_ini_bucket=${{ vars.INI_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RDS_BACKUP_BUCKET }}" ]; then
            echo "TF_VAR_rds_backup_bucket=${{ vars.RDS_BACKUP_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RRRI_TOPIC_ARN }}" ]; then
            echo "TF_VAR_rrri_topic_arn=${{ vars.RRRI_TOPIC_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.SCHEDULE_EXPRESSION }}" ]; then
            echo "TF_VAR_schedule_expression=${{ vars.SCHEDULE_EXPRESSION }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.EVENT_RULE_ARN }}" ]; then
            echo "TF_VAR_event_rule_arn=${{ vars.EVENT_RULE_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.ALARM_ACTION_ARN }}" ]; then
            echo "TF_VAR_alarm_action_arn=${{ vars.ALARM_ACTION_ARN }}" >> $GITHUB_ENV
          fi

      - name: Terraform Init
        run: |
          cd separated-infrastructure/task-deployment
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=task-deployment/${{ env.ENVIRONMENT }}/${{ inputs.task_name }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/task-deployment
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Upload Plan Artifact
        uses: actions/upload-artifact@v4
        if: steps.plan.outputs.exitcode == '2'
        with:
          name: terraform-plan-${{ inputs.task_name }}-${{ env.ENVIRONMENT }}-${{ github.run_number }}
          path: separated-infrastructure/task-deployment/tfplan
          retention-days: 30

      - name: Plan Summary
        run: |
          cd separated-infrastructure/task-deployment
          echo "## Terraform Plan Results" >> $GITHUB_STEP_SUMMARY
          echo "Task: ${{ inputs.task_name }}" >> $GITHUB_STEP_SUMMARY
          echo "Exit Code: ${{ steps.plan.outputs.exitcode }}" >> $GITHUB_STEP_SUMMARY
          echo "- 0: No changes" >> $GITHUB_STEP_SUMMARY
          echo "- 1: Error" >> $GITHUB_STEP_SUMMARY
          echo "- 2: Changes detected" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ steps.plan.outputs.exitcode }}" == "2" ]; then
            echo "### Changes Detected ⚠️" >> $GITHUB_STEP_SUMMARY
            echo "Terraform plan artifact uploaded for review." >> $GITHUB_STEP_SUMMARY
          elif [ "${{ steps.plan.outputs.exitcode }}" == "0" ]; then
            echo "### No Changes ✅" >> $GITHUB_STEP_SUMMARY
          else
            echo "### Plan Failed ❌" >> $GITHUB_STEP_SUMMARY
          fi

  apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: plan
    if: |
      always() && 
      needs.plan.result == 'success' && 
      (inputs.terraform_action == 'apply' || inputs.terraform_action == 'destroy') &&
      github.event_name == 'workflow_dispatch'
    environment: ${{ inputs.environment || 'nonprod' }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_DEFAULT_REGION }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Set Terraform Variables
        run: |
          cd separated-infrastructure/task-deployment
          # Variables are now resolved internally by the module (same as plan step)
          echo "TF_VAR_environment=${{ env.ENVIRONMENT }}" >> $GITHUB_ENV
          echo "TF_VAR_region=${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "TF_VAR_component=processing-apps" >> $GITHUB_ENV
          echo "TF_VAR_component_id=${{ inputs.component_id }}" >> $GITHUB_ENV
          echo "TF_VAR_build_number=${{ github.run_number }}" >> $GITHUB_ENV
          echo "TF_VAR_launched_by=github-actions" >> $GITHUB_ENV
          echo "TF_VAR_launched_on=$(date -u +%Y-%m-%d)" >> $GITHUB_ENV

          # Cluster Configuration
          echo "TF_VAR_use_remote_state=true" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_bucket=${{ vars.TF_STATE_BUCKET }}" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" >> $GITHUB_ENV

          # Task Configuration
          echo "TF_VAR_task_friendly_name=${{ inputs.task_name }}" >> $GITHUB_ENV
          echo "TF_VAR_container_definition_path=./container-definitions/${{ inputs.task_name }}.json" >> $GITHUB_ENV
          echo "TF_VAR_image_url_name_tag=${{ inputs.docker_image_uri || vars.DEFAULT_DOCKER_IMAGE }}" >> $GITHUB_ENV

          # IAM Configuration
          echo "TF_VAR_task_role_arn=${{ vars.TASK_ROLE_ARN }}" >> $GITHUB_ENV
          echo "TF_VAR_execution_role_arn=${{ vars.EXECUTION_ROLE_ARN }}" >> $GITHUB_ENV

          # Optional Configuration (only set if provided)
          if [ -n "${{ vars.INI_BUCKET }}" ]; then
            echo "TF_VAR_ini_bucket=${{ vars.INI_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RDS_BACKUP_BUCKET }}" ]; then
            echo "TF_VAR_rds_backup_bucket=${{ vars.RDS_BACKUP_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RRRI_TOPIC_ARN }}" ]; then
            echo "TF_VAR_rrri_topic_arn=${{ vars.RRRI_TOPIC_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.SCHEDULE_EXPRESSION }}" ]; then
            echo "TF_VAR_schedule_expression=${{ vars.SCHEDULE_EXPRESSION }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.EVENT_RULE_ARN }}" ]; then
            echo "TF_VAR_event_rule_arn=${{ vars.EVENT_RULE_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.ALARM_ACTION_ARN }}" ]; then
            echo "TF_VAR_alarm_action_arn=${{ vars.ALARM_ACTION_ARN }}" >> $GITHUB_ENV
          fi

      - name: Terraform Init
        run: |
          cd separated-infrastructure/task-deployment
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=task-deployment/${{ env.ENVIRONMENT }}/${{ inputs.task_name }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve
          else
            terraform apply
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

      - name: Apply Summary
        run: |
          echo "## Terraform ${{ inputs.terraform_action }} Results" >> $GITHUB_STEP_SUMMARY
          echo "Task: ${{ inputs.task_name }}" >> $GITHUB_STEP_SUMMARY
          echo "Action: ${{ inputs.terraform_action }}" >> $GITHUB_STEP_SUMMARY
          echo "Environment: ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
          echo "Region: ${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_STEP_SUMMARY
          echo "Component ID: ${{ inputs.component_id }}" >> $GITHUB_STEP_SUMMARY

  notify:
    name: Notify Deployment Status
    runs-on: ubuntu-latest
    needs: [validate, plan, apply]
    if: always() && github.event_name == 'workflow_dispatch'
    
    steps:
      - name: Notify Slack
        if: vars.SLACK_WEBHOOK_URL
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: ${{ vars.SLACK_CHANNEL || '#deployments' }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            ECS Task Definition Deployment
            Task: ${{ inputs.task_name }}
            Environment: ${{ env.ENVIRONMENT }}
            Action: ${{ inputs.terraform_action }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
