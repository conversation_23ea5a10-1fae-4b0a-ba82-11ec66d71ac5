name: Deploy Complete ECS Infrastructure

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (nonprod, prod)'
        required: true
        default: 'nonprod'
        type: choice
        options:
          - nonprod
          - prod
      region:
        description: 'AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      component_id:
        description: 'Component ID for tagging'
        required: true
        type: string
      task_names:
        description: 'Comma-separated list of task names to deploy'
        required: true
        type: string
        default: 'US_DescribedVehicleExtract_DotNet'
      docker_image_uri:
        description: 'Docker image URI (ECR) for tasks'
        required: false
        type: string
      terraform_action:
        description: 'Terraform action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      auto_approve:
        description: 'Auto-approve Terraform apply (use with caution)'
        required: false
        default: false
        type: boolean
      deploy_cluster:
        description: 'Deploy cluster infrastructure'
        required: false
        default: true
        type: boolean
      deploy_tasks:
        description: 'Deploy task definitions'
        required: false
        default: true
        type: boolean
      enable_ssm_parameters:
        description: 'Enable SSM Parameter Store integration'
        required: false
        default: false
        type: boolean

env:
  TF_VERSION: '1.5.7'
  AWS_DEFAULT_REGION: ${{ inputs.region || 'us-east-1' }}
  ENVIRONMENT: ${{ inputs.environment || 'nonprod' }}

jobs:
  prepare:
    name: Prepare Deployment
    runs-on: ubuntu-latest
    outputs:
      task_matrix: ${{ steps.matrix.outputs.task_matrix }}
      lambda_folders: ${{ steps.lambda.outputs.lambda_folders }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Prepare Task Matrix
        id: matrix
        run: |
          TASK_NAMES="${{ inputs.task_names }}"
          # Convert comma-separated string to JSON array
          TASK_ARRAY=$(echo "$TASK_NAMES" | jq -R 'split(",") | map(select(length > 0) | gsub("^\\s+|\\s+$"; ""))')
          echo "task_matrix=$TASK_ARRAY" >> $GITHUB_OUTPUT
          echo "Task matrix: $TASK_ARRAY"

      - name: Identify Lambda Functions
        id: lambda
        run: |
          # Check if Lambda functions need to be packaged for cluster deployment
          if [ "${{ inputs.deploy_cluster }}" == "true" ]; then
            LAMBDA_FOLDERS="ecs-termination-protection"
          else
            LAMBDA_FOLDERS=""
          fi
          echo "lambda_folders=$LAMBDA_FOLDERS" >> $GITHUB_OUTPUT
          echo "Lambda folders to package: $LAMBDA_FOLDERS"

  package-lambdas:
    name: Package Lambda Functions
    runs-on: ubuntu-latest
    needs: prepare
    if: needs.prepare.outputs.lambda_folders != ''
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Package Lambda Functions
        uses: ./separated-infrastructure/.github/actions/package-lambda
        with:
          lambda_folders: ${{ needs.prepare.outputs.lambda_folders }}
          lambda_source_path: './infrastructure/lambda'
          lambda_artifact_path: './artifacts/lambda'

  deploy-cluster:
    name: Deploy Cluster Infrastructure
    runs-on: ubuntu-latest
    needs: [prepare, package-lambdas]
    if: always() && inputs.deploy_cluster == true && (needs.package-lambdas.result == 'success' || needs.package-lambdas.result == 'skipped')
    environment: ${{ inputs.environment }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup AWS Credentials
        uses: ./separated-infrastructure/.github/actions/setup-aws-credentials
        with:
          environment: ${{ env.ENVIRONMENT }}
          region: ${{ env.AWS_DEFAULT_REGION }}
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Download Lambda Artifacts
        if: needs.package-lambdas.result == 'success'
        uses: actions/download-artifact@v4
        with:
          name: lambda-packages-${{ github.run_number }}
          path: ./artifacts/lambda

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Create Terraform Variables File
        run: |
          cd separated-infrastructure/cluster-infrastructure
          cat > terraform.tfvars << EOF
          # Core Configuration
          application             = "ais"
          application_abbreviated = "ais"
          service                 = "processing-apps"
          region                  = "${{ env.AWS_DEFAULT_REGION }}"
          region_abbreviated      = "${{ env.AWS_DEFAULT_REGION == 'us-east-1' && 'ue1' || 'uw2' }}"
          environment             = "${{ env.ENVIRONMENT }}"
          component               = "processing-apps"
          component_id            = "${{ inputs.component_id }}"

          # Deployment Metadata
          build_number  = "${{ github.run_number }}"
          launched_by   = "github-actions"
          launched_on   = "$(date -u +%Y-%m-%d)"
          slack_contact = "${{ vars.SLACK_CONTACT || '#devops' }}"

          # Network Configuration
          vpc_name      = "${{ vars.VPC_NAME || 'awsaaia3' }}"
          homenet_cidr  = "${{ vars.HOMENET_CIDR || '10.0.0.0/8' }}"
          ais_cidr      = "${{ vars.AIS_CIDR || '172.16.0.0/12' }}"
          remote_cidr   = "${{ vars.REMOTE_CIDR || '192.168.0.0/16' }}"
          nfs_cidr      = "${{ vars.NFS_CIDR || '10.0.0.0/8' }}"

          # ECS Configuration
          asg_ami              = "${{ vars.ECS_AMI }}"
          instance_type        = "${{ vars.INSTANCE_TYPE || 'm5.4xlarge' }}"
          asg_min_size         = ${{ vars.ASG_MIN_SIZE || 0 }}
          asg_max_size         = ${{ vars.ASG_MAX_SIZE || 10 }}
          asg_desired_capacity = ${{ vars.ASG_DESIRED_CAPACITY || 2 }}

          # EFS Configuration
          efs_id             = "${{ vars.EFS_ID }}"
          efs_security_group = "${{ vars.EFS_SECURITY_GROUP }}"

          # Lambda Configuration
          package_path_ecs_termination_protection = "./artifacts/lambda/ecs-termination-protection.zip"
          lambda_role_arn                        = "${{ vars.LAMBDA_ROLE_ARN }}"
          account_type                           = "${{ env.ENVIRONMENT }}"

          # Auto Scaling Schedule
          asg_scheduling_enabled = "${{ vars.ASG_SCHEDULING_ENABLED || 'false' }}"
          EOF

      - name: Terraform Init
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply' && steps.plan.outputs.exitcode == '2'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve tfplan
          else
            terraform apply tfplan
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

  deploy-tasks:
    name: Deploy Task Definitions
    runs-on: ubuntu-latest
    needs: [prepare, deploy-cluster]
    if: always() && inputs.deploy_tasks == true && (needs.deploy-cluster.result == 'success' || needs.deploy-cluster.result == 'skipped')
    environment: ${{ inputs.environment }}
    strategy:
      matrix:
        task_name: ${{ fromJson(needs.prepare.outputs.task_matrix) }}
      fail-fast: false
      max-parallel: 3
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup AWS Credentials
        uses: ./separated-infrastructure/.github/actions/setup-aws-credentials
        with:
          environment: ${{ env.ENVIRONMENT }}
          region: ${{ env.AWS_DEFAULT_REGION }}
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Create Terraform Variables File
        run: |
          cd separated-infrastructure/task-deployment
          cat > terraform.tfvars << EOF
          # Core Configuration
          application = "ais"
          service     = "processing-apps"
          region      = "${{ env.AWS_DEFAULT_REGION }}"
          environment = "${{ env.ENVIRONMENT }}"
          component   = "processing-apps"
          component_id = "${{ inputs.component_id }}"
          
          # Deployment Metadata
          build_number  = "${{ github.run_number }}"
          launched_by   = "github-actions"
          launched_on   = "$(date -u +%Y-%m-%d)"
          slack_contact = "${{ vars.SLACK_CONTACT || '#devops' }}"
          
          # Cluster Configuration (using remote state)
          use_remote_state     = true
          cluster_state_bucket = "${{ vars.TF_STATE_BUCKET }}"
          cluster_state_key    = "cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate"
          
          # Task Configuration
          task_friendly_name        = "${{ matrix.task_name }}"
          container_definition_path = "./container-definitions/${{ matrix.task_name }}.json"
          
          # Container Configuration
          image_url_name_tag = "${{ inputs.docker_image_uri || vars.DEFAULT_DOCKER_IMAGE }}"
          dotnet_env         = "${{ env.ENVIRONMENT }}"
          
          # IAM Configuration
          task_role_arn      = "${{ vars.TASK_ROLE_ARN }}"
          execution_role_arn = "${{ vars.EXECUTION_ROLE_ARN }}"
          
          # Application Configuration
          ini_bucket         = "${{ vars.INI_BUCKET }}"
          rds_backup_bucket  = "${{ vars.RDS_BACKUP_BUCKET }}"
          rrri_topic_arn     = "${{ vars.RRRI_TOPIC_ARN }}"
          
          # Scheduling Configuration
          schedule_expression = "${{ vars.SCHEDULE_EXPRESSION || '' }}"
          enabled            = ${{ vars.TASK_ENABLED || true }}
          event_rule_arn     = "${{ vars.EVENT_RULE_ARN }}"
          
          # SSM Parameter Store Configuration
          enable_ssm_parameters = ${{ inputs.enable_ssm_parameters || false }}
          ssm_parameter_base_path = "/${{ vars.SSM_BASE_PATH || 'ais' }}/${{ env.ENVIRONMENT }}"
          
          # Monitoring Configuration
          create_failure_alarm = ${{ vars.CREATE_FAILURE_ALARM || true }}
          alarm_action_arn     = "${{ vars.ALARM_ACTION_ARN }}"
          EOF

      - name: Terraform Init
        run: |
          cd separated-infrastructure/task-deployment
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=task-deployment/${{ env.ENVIRONMENT }}/${{ matrix.task_name }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/task-deployment
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply' && steps.plan.outputs.exitcode == '2'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve tfplan
          else
            terraform apply tfplan
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

  deployment-summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [prepare, deploy-cluster, deploy-tasks]
    if: always()
    
    steps:
      - name: Generate Summary
        run: |
          echo "## Complete Infrastructure Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | Action |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|--------|" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ inputs.deploy_cluster }}" == "true" ]; then
            CLUSTER_STATUS="${{ needs.deploy-cluster.result }}"
            if [ "$CLUSTER_STATUS" == "success" ]; then
              echo "| Cluster Infrastructure | ✅ Success | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            else
              echo "| Cluster Infrastructure | ❌ $CLUSTER_STATUS | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "| Cluster Infrastructure | ⏭️ Skipped | - |" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ inputs.deploy_tasks }}" == "true" ]; then
            TASKS_STATUS="${{ needs.deploy-tasks.result }}"
            if [ "$TASKS_STATUS" == "success" ]; then
              echo "| Task Definitions | ✅ Success | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            else
              echo "| Task Definitions | ❌ $TASKS_STATUS | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "| Task Definitions | ⏭️ Skipped | - |" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Configuration" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
          echo "- Region: ${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_STEP_SUMMARY
          echo "- Component ID: ${{ inputs.component_id }}" >> $GITHUB_STEP_SUMMARY
          echo "- Tasks: ${{ inputs.task_names }}" >> $GITHUB_STEP_SUMMARY
          echo "- SSM Parameters: ${{ inputs.enable_ssm_parameters }}" >> $GITHUB_STEP_SUMMARY

      - name: Notify Slack
        if: vars.SLACK_WEBHOOK_URL
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: ${{ vars.SLACK_CHANNEL || '#deployments' }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            🚀 Complete ECS Infrastructure Deployment
            Environment: ${{ env.ENVIRONMENT }}
            Action: ${{ inputs.terraform_action }}
            Cluster: ${{ inputs.deploy_cluster && '✅' || '⏭️' }}
            Tasks: ${{ inputs.deploy_tasks && '✅' || '⏭️' }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
