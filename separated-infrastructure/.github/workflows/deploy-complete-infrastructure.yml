name: Deploy Complete ECS Infrastructure

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (nonprod, prod)'
        required: true
        default: 'nonprod'
        type: choice
        options:
          - nonprod
          - prod
      region:
        description: 'AWS region'
        required: true
        default: 'us-east-1'
        type: choice
        options:
          - us-east-1
          - us-west-2
      component_id:
        description: 'Component ID for tagging'
        required: true
        type: string
      task_names:
        description: 'Comma-separated list of task names to deploy'
        required: true
        type: string
        default: 'US_DescribedVehicleExtract_DotNet'
      docker_image_uri:
        description: 'Docker image URI (ECR) for tasks'
        required: false
        type: string
      terraform_action:
        description: 'Terraform action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy
      auto_approve:
        description: 'Auto-approve Terraform apply (use with caution)'
        required: false
        default: false
        type: boolean
      deploy_cluster:
        description: 'Deploy cluster infrastructure'
        required: false
        default: true
        type: boolean
      deploy_tasks:
        description: 'Deploy task definitions'
        required: false
        default: true
        type: boolean


env:
  TF_VERSION: '1.5.7'
  AWS_DEFAULT_REGION: ${{ inputs.region || 'us-east-1' }}
  ENVIRONMENT: ${{ inputs.environment || 'nonprod' }}

jobs:
  prepare:
    name: Prepare Deployment
    runs-on: ubuntu-latest
    outputs:
      task_matrix: ${{ steps.matrix.outputs.task_matrix }}
      lambda_folders: ${{ steps.lambda.outputs.lambda_folders }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Prepare Task Matrix
        id: matrix
        run: |
          TASK_NAMES="${{ inputs.task_names }}"
          # Convert comma-separated string to JSON array
          TASK_ARRAY=$(echo "$TASK_NAMES" | jq -R 'split(",") | map(select(length > 0) | gsub("^\\s+|\\s+$"; ""))')
          echo "task_matrix=$TASK_ARRAY" >> $GITHUB_OUTPUT
          echo "Task matrix: $TASK_ARRAY"

      - name: Identify Lambda Functions
        id: lambda
        run: |
          # Check if Lambda functions need to be packaged for cluster deployment
          if [ "${{ inputs.deploy_cluster }}" == "true" ]; then
            LAMBDA_FOLDERS="ecs-termination-protection"
          else
            LAMBDA_FOLDERS=""
          fi
          echo "lambda_folders=$LAMBDA_FOLDERS" >> $GITHUB_OUTPUT
          echo "Lambda folders to package: $LAMBDA_FOLDERS"

  package-lambdas:
    name: Package Lambda Functions
    runs-on: ubuntu-latest
    needs: prepare
    if: needs.prepare.outputs.lambda_folders != ''
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Package Lambda Functions
        uses: ./separated-infrastructure/.github/actions/package-lambda
        with:
          lambda_folders: ${{ needs.prepare.outputs.lambda_folders }}
          lambda_source_path: './infrastructure/lambda'
          lambda_artifact_path: './artifacts/lambda'

  deploy-cluster:
    name: Deploy Cluster Infrastructure
    runs-on: ubuntu-latest
    needs: [prepare, package-lambdas]
    if: always() && inputs.deploy_cluster == true && (needs.package-lambdas.result == 'success' || needs.package-lambdas.result == 'skipped')
    environment: ${{ inputs.environment }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup AWS Credentials
        uses: ./separated-infrastructure/.github/actions/setup-aws-credentials
        with:
          environment: ${{ env.ENVIRONMENT }}
          region: ${{ env.AWS_DEFAULT_REGION }}
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Download Lambda Artifacts
        if: needs.package-lambdas.result == 'success'
        uses: actions/download-artifact@v4
        with:
          name: lambda-packages-${{ github.run_number }}
          path: ./artifacts/lambda

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Set Terraform Variables
        run: |
          cd separated-infrastructure/cluster-infrastructure
          # Variables are now resolved internally by the module
          echo "TF_VAR_environment=${{ env.ENVIRONMENT }}" >> $GITHUB_ENV
          echo "TF_VAR_region=${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "TF_VAR_component=processing-apps" >> $GITHUB_ENV
          echo "TF_VAR_component_id=${{ inputs.component_id }}" >> $GITHUB_ENV
          echo "TF_VAR_build_number=${{ github.run_number }}" >> $GITHUB_ENV
          echo "TF_VAR_launched_by=github-actions" >> $GITHUB_ENV
          echo "TF_VAR_launched_on=$(date -u +%Y-%m-%d)" >> $GITHUB_ENV
          echo "TF_VAR_package_path_ecs_termination_protection=./artifacts/lambda/ecs-termination-protection.zip" >> $GITHUB_ENV

      - name: Terraform Init
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/cluster-infrastructure
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply' && steps.plan.outputs.exitcode == '2'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve tfplan
          else
            terraform apply tfplan
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/cluster-infrastructure
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

  deploy-tasks:
    name: Deploy Task Definitions
    runs-on: ubuntu-latest
    needs: [prepare, deploy-cluster]
    if: always() && inputs.deploy_tasks == true && (needs.deploy-cluster.result == 'success' || needs.deploy-cluster.result == 'skipped')
    environment: ${{ inputs.environment }}
    strategy:
      matrix:
        task_name: ${{ fromJson(needs.prepare.outputs.task_matrix) }}
      fail-fast: false
      max-parallel: 3
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup AWS Credentials
        uses: ./separated-infrastructure/.github/actions/setup-aws-credentials
        with:
          environment: ${{ env.ENVIRONMENT }}
          region: ${{ env.AWS_DEFAULT_REGION }}
          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}

      - name: Set Terraform Variables
        run: |
          cd separated-infrastructure/task-deployment
          # Variables are now resolved internally by the module
          echo "TF_VAR_environment=${{ env.ENVIRONMENT }}" >> $GITHUB_ENV
          echo "TF_VAR_region=${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_ENV
          echo "TF_VAR_component=processing-apps" >> $GITHUB_ENV
          echo "TF_VAR_component_id=${{ inputs.component_id }}" >> $GITHUB_ENV
          echo "TF_VAR_build_number=${{ github.run_number }}" >> $GITHUB_ENV
          echo "TF_VAR_launched_by=github-actions" >> $GITHUB_ENV
          echo "TF_VAR_launched_on=$(date -u +%Y-%m-%d)" >> $GITHUB_ENV

          # Cluster Configuration
          echo "TF_VAR_use_remote_state=true" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_bucket=${{ vars.TF_STATE_BUCKET }}" >> $GITHUB_ENV
          echo "TF_VAR_cluster_state_key=cluster-infrastructure/${{ env.ENVIRONMENT }}/terraform.tfstate" >> $GITHUB_ENV

          # Task Configuration
          echo "TF_VAR_task_friendly_name=${{ matrix.task_name }}" >> $GITHUB_ENV
          echo "TF_VAR_container_definition_path=./container-definitions/${{ matrix.task_name }}.json" >> $GITHUB_ENV
          echo "TF_VAR_image_url_name_tag=${{ inputs.docker_image_uri || vars.DEFAULT_DOCKER_IMAGE }}" >> $GITHUB_ENV

          # IAM Configuration
          echo "TF_VAR_task_role_arn=${{ vars.TASK_ROLE_ARN }}" >> $GITHUB_ENV
          echo "TF_VAR_execution_role_arn=${{ vars.EXECUTION_ROLE_ARN }}" >> $GITHUB_ENV

          # Optional Configuration (only set if provided)
          if [ -n "${{ vars.INI_BUCKET }}" ]; then
            echo "TF_VAR_ini_bucket=${{ vars.INI_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RDS_BACKUP_BUCKET }}" ]; then
            echo "TF_VAR_rds_backup_bucket=${{ vars.RDS_BACKUP_BUCKET }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.RRRI_TOPIC_ARN }}" ]; then
            echo "TF_VAR_rrri_topic_arn=${{ vars.RRRI_TOPIC_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.SCHEDULE_EXPRESSION }}" ]; then
            echo "TF_VAR_schedule_expression=${{ vars.SCHEDULE_EXPRESSION }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.EVENT_RULE_ARN }}" ]; then
            echo "TF_VAR_event_rule_arn=${{ vars.EVENT_RULE_ARN }}" >> $GITHUB_ENV
          fi
          if [ -n "${{ vars.ALARM_ACTION_ARN }}" ]; then
            echo "TF_VAR_alarm_action_arn=${{ vars.ALARM_ACTION_ARN }}" >> $GITHUB_ENV
          fi

      - name: Terraform Init
        run: |
          cd separated-infrastructure/task-deployment
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=task-deployment/${{ env.ENVIRONMENT }}/${{ matrix.task_name }}/terraform.tfstate" \
            -backend-config="region=${{ env.AWS_DEFAULT_REGION }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Plan
        id: plan
        run: |
          cd separated-infrastructure/task-deployment
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Terraform Apply
        if: inputs.terraform_action == 'apply' && steps.plan.outputs.exitcode == '2'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve tfplan
          else
            terraform apply tfplan
          fi

      - name: Terraform Destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd separated-infrastructure/task-deployment
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

  deployment-summary:
    name: Deployment Summary
    runs-on: ubuntu-latest
    needs: [prepare, deploy-cluster, deploy-tasks]
    if: always()
    
    steps:
      - name: Generate Summary
        run: |
          echo "## Complete Infrastructure Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Component | Status | Action |" >> $GITHUB_STEP_SUMMARY
          echo "|-----------|--------|--------|" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ inputs.deploy_cluster }}" == "true" ]; then
            CLUSTER_STATUS="${{ needs.deploy-cluster.result }}"
            if [ "$CLUSTER_STATUS" == "success" ]; then
              echo "| Cluster Infrastructure | ✅ Success | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            else
              echo "| Cluster Infrastructure | ❌ $CLUSTER_STATUS | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "| Cluster Infrastructure | ⏭️ Skipped | - |" >> $GITHUB_STEP_SUMMARY
          fi
          
          if [ "${{ inputs.deploy_tasks }}" == "true" ]; then
            TASKS_STATUS="${{ needs.deploy-tasks.result }}"
            if [ "$TASKS_STATUS" == "success" ]; then
              echo "| Task Definitions | ✅ Success | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            else
              echo "| Task Definitions | ❌ $TASKS_STATUS | ${{ inputs.terraform_action }} |" >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "| Task Definitions | ⏭️ Skipped | - |" >> $GITHUB_STEP_SUMMARY
          fi
          
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### Configuration" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: ${{ env.ENVIRONMENT }}" >> $GITHUB_STEP_SUMMARY
          echo "- Region: ${{ env.AWS_DEFAULT_REGION }}" >> $GITHUB_STEP_SUMMARY
          echo "- Component ID: ${{ inputs.component_id }}" >> $GITHUB_STEP_SUMMARY
          echo "- Tasks: ${{ inputs.task_names }}" >> $GITHUB_STEP_SUMMARY

      - name: Notify Slack
        if: vars.SLACK_WEBHOOK_URL
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: ${{ vars.SLACK_CHANNEL || '#deployments' }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            🚀 Complete ECS Infrastructure Deployment
            Environment: ${{ env.ENVIRONMENT }}
            Action: ${{ inputs.terraform_action }}
            Cluster: ${{ inputs.deploy_cluster && '✅' || '⏭️' }}
            Tasks: ${{ inputs.deploy_tasks && '✅' || '⏭️' }}
            Status: ${{ job.status }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
