# GitHub Actions Workflows for ECS Infrastructure Deployment

This directory contains GitHub Actions workflows that replace the original Cake build system for deploying ECS infrastructure. The workflows provide native GitHub Actions implementation with improved security, maintainability, and CI/CD integration.

## 🔄 Migration from Cake Build System

### Original Cake System Analysis

The original deployment system used:
- **Cake Build Scripts** (`deployTerraformComponent.cake`) for orchestration
- **PowerShell bootstrapper** (`cake.ps1`) for Cake execution
- **ALKS integration** for AWS credential management
- **Component configuration JSON** files for parameter management
- **Lambda packaging** with NPM dependency management
- **Terraform execution** with shared variable file management

### New GitHub Actions Implementation

The new system provides:
- **Native GitHub Actions** workflows with no Cake dependencies
- **AWS IAM roles/OIDC** or traditional credential management
- **Environment-based configuration** using GitHub variables and secrets
- **Reusable workflows** and composite actions for shared logic
- **Matrix strategies** for multi-task deployments
- **Comprehensive security scanning** and validation

## 📁 Workflow Files

### Core Deployment Workflows

1. **`deploy-cluster-infrastructure.yml`**
   - Deploys ECS cluster, auto scaling, monitoring, and Lambda components
   - Triggered manually or on cluster infrastructure changes
   - Supports plan/apply/destroy operations

2. **`deploy-task-definitions.yml`**
   - Deploys individual ECS task definitions and scheduling
   - Triggered manually or on task deployment changes
   - Supports SSM Parameter Store integration

3. **`deploy-complete-infrastructure.yml`**
   - Orchestrates both cluster and task deployments
   - Matrix strategy for multiple task deployments
   - Comprehensive deployment summary and notifications

### Reusable Components

4. **`terraform-reusable.yml`**
   - Reusable workflow for common Terraform operations
   - Standardized validation, planning, and apply processes
   - Security scanning integration

### Composite Actions

5. **`.github/actions/package-lambda/`**
   - Packages Lambda functions with NPM dependency management
   - Replaces Cake Lambda packaging functionality
   - Uploads artifacts for deployment

6. **`.github/actions/setup-aws-credentials/`**
   - Configures AWS credentials and environment
   - Replaces ALKS functionality
   - Validates AWS access and sets environment variables

## 🚀 Usage Guide

### Prerequisites

1. **Repository Secrets** (required):
   ```
   AWS_ACCESS_KEY_ID       # AWS access key
   AWS_SECRET_ACCESS_KEY   # AWS secret key
   SLACK_WEBHOOK_URL       # Slack notifications (optional)
   ```

2. **Repository Variables** (configure per environment):
   ```
   # Terraform State Configuration
   TF_STATE_BUCKET         # S3 bucket for Terraform state
   TF_STATE_LOCK_TABLE     # DynamoDB table for state locking
   
   # Network Configuration
   VPC_NAME                # VPC name tag
   HOMENET_CIDR           # Home network CIDR
   AIS_CIDR               # AIS network CIDR
   REMOTE_CIDR            # Remote access CIDR
   NFS_CIDR               # NFS access CIDR
   
   # ECS Configuration
   ECS_AMI                # ECS-optimized AMI ID
   INSTANCE_TYPE          # EC2 instance type
   ASG_MIN_SIZE           # Auto scaling group minimum size
   ASG_MAX_SIZE           # Auto scaling group maximum size
   ASG_DESIRED_CAPACITY   # Auto scaling group desired capacity
   
   # EFS Configuration
   EFS_ID                 # EFS file system ID
   EFS_SECURITY_GROUP     # EFS security group ID
   
   # IAM Configuration
   LAMBDA_ROLE_ARN        # Lambda execution role ARN
   TASK_ROLE_ARN          # ECS task role ARN
   EXECUTION_ROLE_ARN     # ECS execution role ARN
   EVENT_RULE_ARN         # EventBridge rule role ARN
   
   # Application Configuration
   DEFAULT_DOCKER_IMAGE   # Default container image URI
   INI_BUCKET             # Configuration S3 bucket
   RDS_BACKUP_BUCKET      # RDS backup S3 bucket
   RRRI_TOPIC_ARN         # RRRI SNS topic ARN
   
   # Monitoring Configuration
   ALARM_ACTION_ARN       # CloudWatch alarm action ARN
   SLACK_CONTACT          # Slack contact channel
   SLACK_CHANNEL          # Slack notification channel
   
   # SSM Configuration
   SSM_BASE_PATH          # SSM parameter base path
   ```

### Manual Deployment

#### Deploy Complete Infrastructure

1. Go to **Actions** → **Deploy Complete ECS Infrastructure**
2. Click **Run workflow**
3. Configure parameters:
   - **Environment**: `nonprod` or `prod`
   - **Region**: `us-east-1` or `us-west-2`
   - **Component ID**: Unique identifier for tagging
   - **Task Names**: Comma-separated list (e.g., `US_DescribedVehicleExtract_DotNet`)
   - **Docker Image URI**: ECR image URI (optional)
   - **Terraform Action**: `plan`, `apply`, or `destroy`
   - **Auto Approve**: Enable for automated apply (use with caution)
   - **Deploy Options**: Choose cluster and/or tasks
   - **SSM Parameters**: Enable Parameter Store integration

#### Deploy Cluster Only

1. Go to **Actions** → **Deploy ECS Cluster Infrastructure**
2. Configure environment, region, and component ID
3. Choose Terraform action

#### Deploy Tasks Only

1. Go to **Actions** → **Deploy ECS Task Definitions**
2. Configure task name and container image
3. Choose Terraform action

### Automated Deployment

Workflows automatically trigger on:
- **Push to main/develop**: Runs validation and planning
- **Pull requests**: Runs validation and security scanning
- **Manual dispatch**: Full deployment capabilities

## 🔧 Configuration Management

### Parameter Mapping from Cake System

| Cake Parameter | GitHub Actions Equivalent | Source |
|----------------|---------------------------|---------|
| `environment` | `inputs.environment` | Workflow input |
| `region` | `inputs.region` | Workflow input |
| `component` | `inputs.component_id` | Workflow input |
| `buildNumber` | `github.run_number` | GitHub context |
| `launchedBy` | `"github-actions"` | Static value |
| `launchedOn` | `date -u +%Y-%m-%d` | Generated |
| `profileName` | N/A (replaced by IAM) | AWS credentials |
| `account` | Derived from environment | Logic |
| `dotnet_env` | `inputs.environment` | Workflow input |

### Variable Precedence

1. **Workflow inputs** (highest priority)
2. **Repository variables** (`vars.VARIABLE_NAME`)
3. **Default values** in workflow files
4. **Environment-specific overrides**

### SSM Parameter Store Integration

Enable SSM parameters by:
1. Setting `enable_ssm_parameters: true` in workflow
2. Configuring `SSM_BASE_PATH` variable
3. Creating parameters in AWS Systems Manager

Parameters follow naming convention:
```
/{SSM_BASE_PATH}/{application}/{environment}/{service}/{parameter_name}
```

## 🔒 Security Features

### Security Scanning

- **Checkov** security scanning for Terraform code
- **SARIF** results uploaded to GitHub Security tab
- **Dependency scanning** for Lambda functions

### Access Control

- **Environment protection rules** for production deployments
- **Required reviewers** for sensitive operations
- **Branch protection** for main/develop branches

### Credential Management

- **GitHub Secrets** for sensitive data
- **AWS IAM roles** recommended over access keys
- **Least privilege** principle for permissions

## 📊 Monitoring and Notifications

### Deployment Status

- **GitHub Actions summary** with detailed results
- **Artifact uploads** for Terraform plans
- **Job status tracking** across workflow steps

### Slack Integration

Configure Slack notifications:
1. Create Slack webhook URL
2. Add `SLACK_WEBHOOK_URL` secret
3. Set `SLACK_CHANNEL` variable
4. Notifications sent on deployment completion

### Logging

- **Structured logging** in all workflow steps
- **Terraform output** captured and displayed
- **Error details** in job summaries

## 🔄 Migration Steps

### From Cake to GitHub Actions

1. **Backup existing deployment scripts**
2. **Configure repository secrets and variables**
3. **Test workflows in non-production environment**
4. **Validate parameter mapping and functionality**
5. **Update CI/CD processes to use new workflows**
6. **Remove Cake build dependencies**

### Validation Checklist

- [ ] All Cake parameters mapped to workflow inputs/variables
- [ ] Lambda packaging produces identical artifacts
- [ ] Terraform state management preserved
- [ ] AWS resource naming consistency maintained
- [ ] Monitoring and alerting functionality preserved
- [ ] Security scanning and validation implemented

## 🆘 Troubleshooting

### Common Issues

1. **Missing Variables**: Check repository variables configuration
2. **AWS Permissions**: Verify IAM roles and policies
3. **Terraform State**: Ensure state bucket and lock table exist
4. **Lambda Packaging**: Check Node.js version and dependencies
5. **Network Access**: Verify VPC and security group configurations

### Debug Steps

1. **Check workflow logs** for detailed error messages
2. **Validate Terraform syntax** locally
3. **Test AWS credentials** with AWS CLI
4. **Review repository variables** and secrets
5. **Compare with working deployments**

## 📚 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Terraform GitHub Actions](https://github.com/hashicorp/setup-terraform)
- [AWS Configure Credentials Action](https://github.com/aws-actions/configure-aws-credentials)
- [Checkov Security Scanner](https://github.com/bridgecrewio/checkov-action)
