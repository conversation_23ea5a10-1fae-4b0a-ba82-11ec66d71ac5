name: Reusable Terraform Workflow

on:
  workflow_call:
    inputs:
      working_directory:
        description: 'Working directory for Terraform operations'
        required: true
        type: string
      environment:
        description: 'Target environment'
        required: true
        type: string
      region:
        description: 'AWS region'
        required: true
        type: string
      terraform_action:
        description: 'Terraform action (plan, apply, destroy)'
        required: true
        type: string
      state_key:
        description: 'Terraform state key'
        required: true
        type: string
      auto_approve:
        description: 'Auto-approve Terraform apply'
        required: false
        type: boolean
        default: false
      terraform_version:
        description: 'Terraform version'
        required: false
        type: string
        default: '1.5.7'
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      SLACK_WEBHOOK_URL:
        required: false
    outputs:
      plan_exitcode:
        description: 'Terraform plan exit code'
        value: ${{ jobs.terraform.outputs.plan_exitcode }}
      apply_status:
        description: 'Terraform apply status'
        value: ${{ jobs.terraform.outputs.apply_status }}

jobs:
  terraform:
    name: Terraform ${{ inputs.terraform_action }}
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment }}
    outputs:
      plan_exitcode: ${{ steps.plan.outputs.exitcode }}
      apply_status: ${{ steps.apply.outcome }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ inputs.region }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ inputs.terraform_version }}

      - name: Terraform Format Check
        id: fmt
        run: |
          cd ${{ inputs.working_directory }}
          terraform fmt -check=true -diff=true
        continue-on-error: true

      - name: Terraform Init
        run: |
          cd ${{ inputs.working_directory }}
          terraform init \
            -backend-config="bucket=${{ vars.TF_STATE_BUCKET }}" \
            -backend-config="key=${{ inputs.state_key }}" \
            -backend-config="region=${{ inputs.region }}" \
            -backend-config="dynamodb_table=${{ vars.TF_STATE_LOCK_TABLE }}"

      - name: Terraform Validate
        id: validate
        run: |
          cd ${{ inputs.working_directory }}
          terraform validate

      - name: Terraform Plan
        id: plan
        if: inputs.terraform_action == 'plan' || inputs.terraform_action == 'apply'
        run: |
          cd ${{ inputs.working_directory }}
          terraform plan -detailed-exitcode -out=tfplan
        continue-on-error: true

      - name: Upload Plan Artifact
        uses: actions/upload-artifact@v4
        if: steps.plan.outputs.exitcode == '2'
        with:
          name: terraform-plan-${{ inputs.environment }}-${{ github.run_number }}
          path: ${{ inputs.working_directory }}/tfplan
          retention-days: 30

      - name: Terraform Apply
        id: apply
        if: inputs.terraform_action == 'apply' && steps.plan.outputs.exitcode == '2'
        run: |
          cd ${{ inputs.working_directory }}
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform apply -auto-approve tfplan
          else
            terraform apply tfplan
          fi

      - name: Terraform Destroy
        id: destroy
        if: inputs.terraform_action == 'destroy'
        run: |
          cd ${{ inputs.working_directory }}
          if [ "${{ inputs.auto_approve }}" == "true" ]; then
            terraform destroy -auto-approve
          else
            terraform destroy
          fi

      - name: Operation Summary
        run: |
          echo "## Terraform ${{ inputs.terraform_action }} Results" >> $GITHUB_STEP_SUMMARY
          echo "Environment: ${{ inputs.environment }}" >> $GITHUB_STEP_SUMMARY
          echo "Region: ${{ inputs.region }}" >> $GITHUB_STEP_SUMMARY
          echo "Working Directory: ${{ inputs.working_directory }}" >> $GITHUB_STEP_SUMMARY
          
          if [ "${{ inputs.terraform_action }}" == "plan" ]; then
            echo "Plan Exit Code: ${{ steps.plan.outputs.exitcode }}" >> $GITHUB_STEP_SUMMARY
            if [ "${{ steps.plan.outputs.exitcode }}" == "2" ]; then
              echo "### Changes Detected ⚠️" >> $GITHUB_STEP_SUMMARY
            elif [ "${{ steps.plan.outputs.exitcode }}" == "0" ]; then
              echo "### No Changes ✅" >> $GITHUB_STEP_SUMMARY
            else
              echo "### Plan Failed ❌" >> $GITHUB_STEP_SUMMARY
            fi
          elif [ "${{ inputs.terraform_action }}" == "apply" ]; then
            echo "Apply Status: ${{ steps.apply.outcome }}" >> $GITHUB_STEP_SUMMARY
          elif [ "${{ inputs.terraform_action }}" == "destroy" ]; then
            echo "Destroy Status: ${{ steps.destroy.outcome }}" >> $GITHUB_STEP_SUMMARY
          fi

      - name: Notify on Failure
        if: failure() && secrets.SLACK_WEBHOOK_URL
        uses: 8398a7/action-slack@v3
        with:
          status: failure
          channel: ${{ vars.SLACK_CHANNEL || '#deployments' }}
          webhook_url: ${{ secrets.SLACK_WEBHOOK_URL }}
          fields: repo,message,commit,author,action,eventName,ref,workflow
          text: |
            🚨 Terraform ${{ inputs.terraform_action }} Failed
            Environment: ${{ inputs.environment }}
            Working Directory: ${{ inputs.working_directory }}
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
