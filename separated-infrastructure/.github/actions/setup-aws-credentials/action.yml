name: 'Setup AWS Credentials'
description: 'Configures AWS credentials for deployment, replacing ALKS functionality'
inputs:
  environment:
    description: 'Target environment (nonprod, prod)'
    required: true
  region:
    description: 'AWS region'
    required: true
  role_duration_seconds:
    description: 'Role session duration in seconds'
    required: false
    default: '3600'
  aws_access_key_id:
    description: 'AWS Access Key ID'
    required: true
  aws_secret_access_key:
    description: 'AWS Secret Access Key'
    required: true
  aws_session_token:
    description: 'AWS Session Token (optional)'
    required: false

outputs:
  aws_account_id:
    description: 'AWS Account ID'
    value: ${{ steps.account.outputs.account_id }}
  aws_region:
    description: 'Configured AWS region'
    value: ${{ inputs.region }}
  account_type:
    description: 'Account type (prod or nonprod)'
    value: ${{ steps.account.outputs.account_type }}

runs:
  using: 'composite'
  steps:
    - name: Configure AWS Credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ inputs.aws_access_key_id }}
        aws-secret-access-key: ${{ inputs.aws_secret_access_key }}
        aws-session-token: ${{ inputs.aws_session_token }}
        aws-region: ${{ inputs.region }}
        role-duration-seconds: ${{ inputs.role_duration_seconds }}

    - name: Verify AWS Configuration
      id: account
      shell: bash
      run: |
        # Get AWS account information
        ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
        echo "account_id=$ACCOUNT_ID" >> $GITHUB_OUTPUT
        
        # Determine account type based on environment
        if [[ "${{ inputs.environment }}" == "prod"* ]]; then
          ACCOUNT_TYPE="prod"
        else
          ACCOUNT_TYPE="nonprod"
        fi
        echo "account_type=$ACCOUNT_TYPE" >> $GITHUB_OUTPUT
        
        echo "AWS Account ID: $ACCOUNT_ID"
        echo "Account Type: $ACCOUNT_TYPE"
        echo "Region: ${{ inputs.region }}"
        
        # Verify we can access AWS services
        aws sts get-caller-identity
        
        # Test S3 access (common requirement for Terraform state)
        aws s3 ls > /dev/null || echo "Warning: S3 access test failed"

    - name: Set Environment Variables
      shell: bash
      run: |
        # Set additional environment variables that may be needed by Terraform
        echo "AWS_DEFAULT_REGION=${{ inputs.region }}" >> $GITHUB_ENV
        echo "AWS_REGION=${{ inputs.region }}" >> $GITHUB_ENV
        echo "TF_VAR_region=${{ inputs.region }}" >> $GITHUB_ENV
        echo "TF_VAR_account_type=${{ steps.account.outputs.account_type }}" >> $GITHUB_ENV

    - name: AWS Configuration Summary
      shell: bash
      run: |
        echo "## AWS Configuration" >> $GITHUB_STEP_SUMMARY
        echo "| Setting | Value |" >> $GITHUB_STEP_SUMMARY
        echo "|---------|-------|" >> $GITHUB_STEP_SUMMARY
        echo "| Account ID | ${{ steps.account.outputs.account_id }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Account Type | ${{ steps.account.outputs.account_type }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Region | ${{ inputs.region }} |" >> $GITHUB_STEP_SUMMARY
        echo "| Environment | ${{ inputs.environment }} |" >> $GITHUB_STEP_SUMMARY
