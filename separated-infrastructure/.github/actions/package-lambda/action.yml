name: 'Package Lambda Functions'
description: 'Packages Lambda functions for deployment, replacing Cake build system functionality'
inputs:
  lambda_source_path:
    description: 'Path to Lambda source directory'
    required: false
    default: '../infrastructure/lambda'
  lambda_artifact_path:
    description: 'Path to Lambda artifact output directory'
    required: false
    default: '../artifacts/lambda'
  lambda_folders:
    description: 'Comma-separated list of Lambda folder names to package'
    required: true
  node_version:
    description: 'Node.js version for NPM dependencies'
    required: false
    default: '18'

outputs:
  packaged_lambdas:
    description: 'JSON array of packaged Lambda information'
    value: ${{ steps.package.outputs.packaged_lambdas }}
  artifact_path:
    description: 'Path to Lambda artifacts directory'
    value: ${{ steps.setup.outputs.artifact_path }}

runs:
  using: 'composite'
  steps:
    - name: Setup Directories
      id: setup
      shell: bash
      run: |
        # Ensure the output directory exists in a clean state
        ARTIFACT_PATH="${{ inputs.lambda_artifact_path }}"
        if [ -d "$ARTIFACT_PATH" ]; then
          rm -rf "$ARTIFACT_PATH"
        fi
        mkdir -p "$ARTIFACT_PATH"
        echo "artifact_path=$ARTIFACT_PATH" >> $GITHUB_OUTPUT
        
        # Verify source directory exists
        SOURCE_PATH="${{ inputs.lambda_source_path }}"
        if [ ! -d "$SOURCE_PATH" ]; then
          echo "Error: Lambda source directory not found at $SOURCE_PATH"
          exit 1
        fi
        
        echo "Lambda source path: $SOURCE_PATH"
        echo "Lambda artifact path: $ARTIFACT_PATH"

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ inputs.node_version }}
        cache: 'npm'
        cache-dependency-path: |
          ${{ inputs.lambda_source_path }}/*/package.json

    - name: Package Lambda Functions
      id: package
      shell: bash
      run: |
        SOURCE_PATH="${{ inputs.lambda_source_path }}"
        ARTIFACT_PATH="${{ inputs.lambda_artifact_path }}"
        LAMBDA_FOLDERS="${{ inputs.lambda_folders }}"
        
        # Convert comma-separated list to array
        IFS=',' read -ra FOLDERS <<< "$LAMBDA_FOLDERS"
        
        PACKAGED_LAMBDAS="[]"
        
        for folder in "${FOLDERS[@]}"; do
          # Trim whitespace
          folder=$(echo "$folder" | xargs)
          
          echo "Processing Lambda function: $folder"
          
          LAMBDA_SOURCE_DIR="$SOURCE_PATH/$folder"
          LAMBDA_ZIP_PATH="$ARTIFACT_PATH/$folder.zip"
          
          # Verify the Lambda source directory exists
          if [ ! -d "$LAMBDA_SOURCE_DIR" ]; then
            echo "Error: Lambda source directory not found at $LAMBDA_SOURCE_DIR"
            exit 1
          fi
          
          # Check if package.json exists and run npm install if needed
          if [ -f "$LAMBDA_SOURCE_DIR/package.json" ]; then
            echo "Found package.json, running npm install for $folder"
            cd "$LAMBDA_SOURCE_DIR"
            npm ci --production
            cd - > /dev/null
          fi
          
          # Create zip file
          echo "Creating zip file: $LAMBDA_ZIP_PATH"
          cd "$LAMBDA_SOURCE_DIR"
          zip -r "$LAMBDA_ZIP_PATH" . -x "*.git*" "*.DS_Store*" "node_modules/.cache/*"
          cd - > /dev/null
          
          # Verify zip was created successfully
          if [ ! -f "$LAMBDA_ZIP_PATH" ]; then
            echo "Error: Failed to create zip file for $folder"
            exit 1
          fi
          
          # Get zip file size for logging
          ZIP_SIZE=$(stat -f%z "$LAMBDA_ZIP_PATH" 2>/dev/null || stat -c%s "$LAMBDA_ZIP_PATH" 2>/dev/null || echo "unknown")
          echo "Created $folder.zip (size: $ZIP_SIZE bytes)"
          
          # Add to packaged lambdas JSON array
          LAMBDA_INFO=$(jq -n \
            --arg name "$folder" \
            --arg path "$LAMBDA_ZIP_PATH" \
            --arg size "$ZIP_SIZE" \
            '{name: $name, path: $path, size: $size}')
          
          PACKAGED_LAMBDAS=$(echo "$PACKAGED_LAMBDAS" | jq ". += [$LAMBDA_INFO]")
        done
        
        echo "packaged_lambdas=$PACKAGED_LAMBDAS" >> $GITHUB_OUTPUT
        echo "Successfully packaged ${#FOLDERS[@]} Lambda functions"

    - name: Upload Lambda Artifacts
      uses: actions/upload-artifact@v4
      with:
        name: lambda-packages-${{ github.run_number }}
        path: ${{ inputs.lambda_artifact_path }}/*.zip
        retention-days: 30

    - name: Package Summary
      shell: bash
      run: |
        echo "## Lambda Packaging Results" >> $GITHUB_STEP_SUMMARY
        echo "| Function | Status | Size |" >> $GITHUB_STEP_SUMMARY
        echo "|----------|--------|------|" >> $GITHUB_STEP_SUMMARY
        
        PACKAGED_LAMBDAS='${{ steps.package.outputs.packaged_lambdas }}'
        echo "$PACKAGED_LAMBDAS" | jq -r '.[] | "| \(.name) | ✅ Packaged | \(.size) bytes |"' >> $GITHUB_STEP_SUMMARY
