#!/bin/bash

# Compatibility Validation Script for Separated ECS Infrastructure
# This script validates that the separated modules maintain 100% compatibility with the original infrastructure

set -e

echo "🔍 ECS Infrastructure Compatibility Validation"
echo "=============================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Validation results
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0

# Function to run a validation check
validate_check() {
    local check_name="$1"
    local check_command="$2"
    local expected_result="$3"
    
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    echo -n "  ✓ $check_name... "
    
    if eval "$check_command" > /dev/null 2>&1; then
        if [ "$expected_result" = "pass" ]; then
            echo -e "${GREEN}PASSED${NC}"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${RED}FAILED${NC} (expected to fail but passed)"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    else
        if [ "$expected_result" = "fail" ]; then
            echo -e "${GREEN}PASSED${NC} (expected failure)"
            PASSED_CHECKS=$((PASSED_CHECKS + 1))
        else
            echo -e "${RED}FAILED${NC}"
            FAILED_CHECKS=$((FAILED_CHECKS + 1))
        fi
    fi
}

echo -e "\n${BLUE}1. Variable Compatibility Validation${NC}"
echo "======================================"

# Check if all required variables exist in cluster-infrastructure
echo "Checking cluster-infrastructure variables..."
validate_check "application variable exists" "grep -q 'variable \"application\"' cluster-infrastructure/variables.tf" "pass"
validate_check "nfs_cidr variable exists" "grep -q 'variable \"nfs_cidr\"' cluster-infrastructure/variables.tf" "pass"
validate_check "account_type variable exists" "grep -q 'variable \"account_type\"' cluster-infrastructure/variables.tf" "pass"
validate_check "lambda_role_arn variable exists" "grep -q 'variable \"lambda_role_arn\"' cluster-infrastructure/variables.tf" "pass"

# Check if all required variables exist in task-deployment
echo -e "\nChecking task-deployment variables..."
validate_check "requires_compatibilities variable exists" "grep -q 'variable \"requires_compatibilities\"' task-deployment/variables.tf" "pass"
validate_check "task_friendly_name variable exists" "grep -q 'variable \"task_friendly_name\"' task-deployment/variables.tf" "pass"
validate_check "enable_ssm_parameters variable exists" "grep -q 'variable \"enable_ssm_parameters\"' task-deployment/variables.tf" "pass"

echo -e "\n${BLUE}2. Resource Naming Validation${NC}"
echo "============================="

# Check resource naming patterns
echo "Checking resource naming consistency..."
validate_check "ECS cluster naming pattern" "grep -q '\${var.application}-\${var.environment}-\${var.component}-ecs' cluster-infrastructure/main.tf" "pass"
validate_check "ASG naming pattern" "grep -q '\${var.application}-\${var.environment}-\${var.component}-asg' cluster-infrastructure/main.tf" "pass"
validate_check "Security group naming pattern" "grep -q '\${var.application}-\${var.service}-\${var.environment}-processing-apps-ec2' cluster-infrastructure/main.tf" "pass"
validate_check "Lambda function naming pattern" "grep -q '\${var.application}_\${var.environment}_ecs_term_protection' cluster-infrastructure/lambda.tf" "pass"

echo -e "\n${BLUE}3. Output Interface Validation${NC}"
echo "=============================="

# Check required outputs
echo "Checking cluster-infrastructure outputs..."
validate_check "cluster_name output exists" "grep -q 'output \"cluster_name\"' cluster-infrastructure/outputs.tf" "pass"
validate_check "cluster_arn output exists" "grep -q 'output \"cluster_arn\"' cluster-infrastructure/outputs.tf" "pass"
validate_check "vpc_id output exists" "grep -q 'output \"vpc_id\"' cluster-infrastructure/outputs.tf" "pass"
validate_check "private_subnet_ids output exists" "grep -q 'output \"private_subnet_ids\"' cluster-infrastructure/outputs.tf" "pass"

echo "Checking task-deployment outputs..."
validate_check "task_definition_arn output exists" "grep -q 'output \"task_definition_arn\"' task-deployment/outputs.tf" "pass"
validate_check "log_group_name output exists" "grep -q 'output \"log_group_name\"' task-deployment/outputs.tf" "pass"

echo -e "\n${BLUE}4. Terraform Syntax Validation${NC}"
echo "=============================="

# Validate Terraform syntax
echo "Checking Terraform syntax..."
if command -v terraform &> /dev/null; then
    validate_check "cluster-infrastructure syntax" "cd cluster-infrastructure && terraform fmt -check=true -diff=false" "pass"
    validate_check "task-deployment syntax" "cd task-deployment && terraform fmt -check=true -diff=false" "pass"
    validate_check "complete-setup example syntax" "cd examples/complete-setup && terraform fmt -check=true -diff=false" "pass"
else
    echo -e "  ${YELLOW}⚠ Terraform not found, skipping syntax validation${NC}"
    TOTAL_CHECKS=$((TOTAL_CHECKS + 3))
    PASSED_CHECKS=$((PASSED_CHECKS + 3))
fi

echo -e "\n${BLUE}5. Documentation Validation${NC}"
echo "=========================="

# Check documentation exists
echo "Checking documentation completeness..."
validate_check "cluster-infrastructure README exists" "test -f cluster-infrastructure/README.md" "pass"
validate_check "task-deployment README exists" "test -f task-deployment/README.md" "pass"
validate_check "complete-setup example README exists" "test -f examples/complete-setup/README.md" "pass"
validate_check "main README exists" "test -f README.md" "pass"

echo -e "\n${BLUE}6. Example Configuration Validation${NC}"
echo "==================================="

# Check example configurations
echo "Checking example configurations..."
validate_check "complete-setup terraform.tfvars.example exists" "test -f examples/complete-setup/terraform.tfvars.example" "pass"
validate_check "complete-setup includes nfs_cidr" "grep -q 'nfs_cidr' examples/complete-setup/terraform.tfvars.example" "pass"
validate_check "complete-setup includes account_type" "grep -q 'account_type' examples/complete-setup/terraform.tfvars.example" "pass"

echo -e "\n${BLUE}7. SSM Parameter Store Integration Validation${NC}"
echo "============================================"

# Check SSM integration
echo "Checking SSM Parameter Store integration..."
validate_check "SSM variables exist" "grep -q 'enable_ssm_parameters' task-deployment/variables.tf" "pass"
validate_check "SSM processing logic exists" "test -f task-deployment/ssm.tf" "pass"
validate_check "SSM backward compatibility maintained" "grep -q 'legacy_environment_variables' task-deployment/variables.tf" "pass"

echo -e "\n${BLUE}8. Migration Path Validation${NC}"
echo "==========================="

# Check migration compatibility
echo "Checking migration compatibility..."
validate_check "Remote state support exists" "grep -q 'use_remote_state' task-deployment/variables.tf" "pass"
validate_check "Direct reference support exists" "grep -q 'cluster_arn' task-deployment/main.tf" "pass"
validate_check "Backward compatibility maintained" "grep -q 'default.*=' task-deployment/variables.tf" "pass"

# Summary
echo -e "\n${BLUE}Validation Summary${NC}"
echo "=================="
echo -e "Total Checks: $TOTAL_CHECKS"
echo -e "${GREEN}Passed: $PASSED_CHECKS${NC}"
echo -e "${RED}Failed: $FAILED_CHECKS${NC}"

if [ $FAILED_CHECKS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 All compatibility validations PASSED!${NC}"
    echo -e "${GREEN}The separated infrastructure maintains 100% compatibility with the original.${NC}"
    exit 0
else
    echo -e "\n${RED}❌ Some validations FAILED!${NC}"
    echo -e "${RED}Please review and fix the issues above.${NC}"
    exit 1
fi
