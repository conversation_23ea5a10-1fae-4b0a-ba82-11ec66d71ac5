# Migration Guide: From Monolithic to Separated ECS Infrastructure

This guide provides step-by-step instructions for migrating from the original monolithic ECS infrastructure to the separated cluster-infrastructure and task-deployment modules.

## Overview

The migration enables:
- **Repository Independence**: Infrastructure and application teams can work independently
- **Improved Maintainability**: Clear separation of concerns
- **Enhanced Security**: SSM Parameter Store integration for sensitive configuration
- **Zero Downtime**: Seamless migration without service interruption

## Prerequisites

Before starting the migration:

1. **Backup Current State**: Ensure Terraform state files are backed up
2. **Test Environment**: Validate the migration process in a non-production environment first
3. **Access Permissions**: Ensure you have necessary AWS and Terraform permissions
4. **Dependencies**: Verify all required IAM roles, VPC, and EFS resources exist

## Migration Strategies

### Strategy 1: Gradual Migration (Recommended)

This approach allows for gradual migration with minimal risk:

#### Phase 1: Deploy Separated Cluster Infrastructure

1. **Create new cluster infrastructure**:
   ```bash
   cd separated-infrastructure/cluster-infrastructure
   cp terraform.tfvars.example terraform.tfvars
   # Edit terraform.tfvars with your values
   ```

2. **Configure variables** (use same values as original):
   ```hcl
   # terraform.tfvars
   application             = "ais"
   application_abbreviated = "ais"
   service                 = "processing-apps"
   region                  = "us-east-1"
   region_abbreviated      = "ue1"
   environment             = "nonprod"
   component               = "processing-apps"
   
   # Network Configuration (same as original)
   vpc_name      = "awsaaia3"
   homenet_cidr  = "10.0.0.0/8"
   ais_cidr      = "**********/12"
   remote_cidr   = "***********/16"
   nfs_cidr      = "10.0.0.0/8"
   
   # ECS Configuration (same as original)
   asg_ami              = "ami-0abcdef1234567890"
   instance_type        = "m5.4xlarge"
   asg_min_size         = 3
   asg_max_size         = 18
   asg_desired_capacity = 3
   
   # Lambda Configuration
   package_path_ecs_termination_protection = "../../lambda/ecs-termination-protection.zip"
   lambda_role_arn                        = "arn:aws:iam::************:role/lambda-execution-role"
   account_type                           = "nonprod"
   ```

3. **Import existing resources**:
   ```bash
   # Initialize Terraform
   terraform init
   
   # Import existing resources (adjust resource names as needed)
   terraform import aws_ecs_cluster.main ais-nonprod-processing-apps-ecs
   terraform import aws_autoscaling_group.ecs_instances ais-nonprod-processing-apps-asg
   terraform import aws_security_group.ecs_instances sg-xxxxxxxxx
   terraform import aws_lambda_function.ecs_termination_protection ais_nonprod_ecs_term_protection
   
   # Import CloudWatch alarms
   terraform import aws_cloudwatch_metric_alarm.low_cpu ais-nonprod-processing-apps-CPU-Low
   terraform import aws_cloudwatch_metric_alarm.high_cpu ais-nonprod-processing-apps-CPU-High
   # ... continue for all alarms
   ```

4. **Validate and apply**:
   ```bash
   terraform plan  # Should show no changes if import was successful
   terraform apply
   ```

#### Phase 2: Deploy Task Definitions

1. **Create task deployment**:
   ```bash
   cd ../task-deployment
   cp terraform.tfvars.example terraform.tfvars
   ```

2. **Configure to reference cluster**:
   ```hcl
   # terraform.tfvars
   use_remote_state     = true
   cluster_state_bucket = "your-terraform-state-bucket"
   cluster_state_key    = "cluster-infrastructure/terraform.tfstate"
   
   # Task configuration
   task_friendly_name        = "US_DescribedVehicleExtract_DotNet"
   container_definition_path = "./container-definitions/US_DescribedVehicleExtract_DotNet.json"
   
   # IAM roles
   task_role_arn      = "arn:aws:iam::************:role/ecs-task-role"
   execution_role_arn = "arn:aws:iam::************:role/ecs-execution-role"
   
   # Container image
   image_url_name_tag = "************.dkr.ecr.us-east-1.amazonaws.com/your-app:latest"
   ```

3. **Import existing task definitions**:
   ```bash
   terraform init
   terraform import aws_ecs_task_definition.main US_DescribedVehicleExtract_DotNet_nonprod:1
   terraform import aws_cloudwatch_log_group.task_logs US_DescribedVehicleExtract_DotNet_nonprod
   ```

4. **Deploy**:
   ```bash
   terraform plan
   terraform apply
   ```

#### Phase 3: Cleanup Original Infrastructure

1. **Verify new infrastructure works**:
   - Test task execution
   - Verify monitoring and alerting
   - Confirm auto scaling functionality

2. **Remove original infrastructure**:
   ```bash
   cd ../../infrastructure/environments/dotnet/processing-apps-cluster
   terraform destroy
   
   cd ../task-definitions
   terraform destroy
   ```

### Strategy 2: Side-by-Side Migration

Deploy new infrastructure alongside existing, then switch over:

1. **Deploy with different names**:
   ```hcl
   component = "processing-apps-v2"  # Use different component name
   ```

2. **Test thoroughly**

3. **Switch traffic** and **cleanup old infrastructure**

## Variable Mapping

### Original → Separated Cluster Infrastructure

| Original Variable | Separated Variable | Notes |
|------------------|-------------------|-------|
| `application` | `application` | ✅ Same |
| `service` | `service` | ✅ Same |
| `environment` | `environment` | ✅ Same |
| `component` | `component` | ✅ Same |
| `vpc_id` | `vpc_name` | ⚠️ Now uses VPC name tag |
| `private_subnet_ids` | Auto-discovered | ⚠️ Uses subnet tags |
| `security_group` | `efs_security_group` | ✅ Same purpose |
| `asg_ami` | `asg_ami` | ✅ Same |
| `package_path_ecs_termination_protection` | `package_path_ecs_termination_protection` | ✅ Same |

### Original → Separated Task Deployment

| Original Variable | Separated Variable | Notes |
|------------------|-------------------|-------|
| `task_friendly_name` | `task_friendly_name` | ✅ Same |
| `requires_compatibilites` | `requires_compatibilities` | ⚠️ Fixed typo |
| `image_url_name_tag` | `image_url_name_tag` | ✅ Same |
| `ini_bucket` | `ini_bucket` | ✅ Same |
| `dotnet_env` | `dotnet_env` | ✅ Same |

## Resource Name Mapping

All AWS resource names remain identical:

| Resource Type | Naming Pattern | Example |
|--------------|----------------|---------|
| ECS Cluster | `${app}-${env}-${component}-ecs` | `ais-nonprod-processing-apps-ecs` |
| Auto Scaling Group | `${app}-${env}-${component}-asg` | `ais-nonprod-processing-apps-asg` |
| Security Group | `${app}-${service}-${env}-processing-apps-ec2` | `ais-processing-apps-nonprod-processing-apps-ec2` |
| Lambda Function | `${app}_${env}_ecs_term_protection` | `ais_nonprod_ecs_term_protection` |
| Task Definition | `${task_name}_${env}` | `US_DescribedVehicleExtract_DotNet_nonprod` |

## SSM Parameter Store Migration

### Enable SSM Parameters

1. **Create parameters**:
   ```bash
   cd examples/ssm-parameters
   terraform apply
   ```

2. **Update task deployment**:
   ```hcl
   enable_ssm_parameters = true
   ssm_parameters = {
     "DATABASE_PASSWORD" = {
       name = "common/database/password"
       type = "SecureString"
       description = "Database password"
     }
   }
   ```

3. **Update container definitions** to use `secrets` field

### Gradual SSM Migration

1. **Start with non-sensitive parameters**
2. **Test thoroughly**
3. **Migrate sensitive parameters**
4. **Remove hardcoded values**

## Validation

Run the compatibility validation script:

```bash
cd separated-infrastructure
./validate-compatibility.sh
```

## Troubleshooting

### Common Issues

1. **Resource Already Exists**:
   ```bash
   # Import the existing resource
   terraform import aws_ecs_cluster.main existing-cluster-name
   ```

2. **State File Conflicts**:
   ```bash
   # Use different state file locations
   terraform init -backend-config="key=cluster-v2/terraform.tfstate"
   ```

3. **Permission Denied**:
   - Verify IAM roles have necessary permissions
   - Check resource policies

### Rollback Plan

If issues occur:

1. **Keep original infrastructure** until migration is validated
2. **Use different component names** during testing
3. **Have state file backups** ready
4. **Document all import commands** for easy rollback

## Post-Migration

1. **Update CI/CD pipelines** to use new module structure
2. **Train teams** on new repository structure
3. **Update documentation** and runbooks
4. **Monitor** for any issues
5. **Cleanup** old infrastructure after validation period

## Support

For issues during migration:
1. Check the validation script output
2. Review Terraform plan carefully
3. Test in non-production first
4. Keep detailed logs of all commands executed
