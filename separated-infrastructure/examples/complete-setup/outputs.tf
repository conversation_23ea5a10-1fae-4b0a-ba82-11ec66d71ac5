# Cluster Infrastructure Outputs
output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = module.ecs_cluster.cluster_name
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = module.ecs_cluster.cluster_arn
}

output "vpc_id" {
  description = "ID of the VPC"
  value       = module.ecs_cluster.vpc_id
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = module.ecs_cluster.private_subnet_ids
}

output "ecs_security_group_id" {
  description = "ID of the ECS instances security group"
  value       = module.ecs_cluster.ecs_security_group_id
}

output "autoscaling_group_name" {
  description = "Name of the Auto Scaling Group"
  value       = module.ecs_cluster.autoscaling_group_name
}

output "lambda_function_arn" {
  description = "ARN of the ECS termination protection Lambda function"
  value       = module.ecs_cluster.lambda_function_arn
}

# Task Deployment Outputs
output "task_definition_arn" {
  description = "ARN of the main task definition"
  value       = module.describe_vehicle_extract_task.task_definition_arn
}

output "task_definition_family" {
  description = "Family name of the main task definition"
  value       = module.describe_vehicle_extract_task.task_definition_family
}

output "log_group_name" {
  description = "Name of the CloudWatch log group for main task"
  value       = module.describe_vehicle_extract_task.log_group_name
}

output "scheduled_rule_name" {
  description = "Name of the CloudWatch Events rule for main task"
  value       = module.describe_vehicle_extract_task.scheduled_rule_name
}

output "failure_alarm_name" {
  description = "Name of the failure CloudWatch alarm for main task"
  value       = module.describe_vehicle_extract_task.failure_alarm_name
}

# Additional Task Outputs (if deployed)
output "additional_task_definition_arn" {
  description = "ARN of the additional task definition"
  value       = var.deploy_additional_task ? module.additional_task[0].task_definition_arn : null
}

output "additional_log_group_name" {
  description = "Name of the CloudWatch log group for additional task"
  value       = var.deploy_additional_task ? module.additional_task[0].log_group_name : null
}

# Summary Information
output "deployment_summary" {
  description = "Summary of the deployment"
  value = {
    cluster_name           = module.ecs_cluster.cluster_name
    cluster_arn           = module.ecs_cluster.cluster_arn
    main_task_name        = module.describe_vehicle_extract_task.task_friendly_name
    main_task_arn         = module.describe_vehicle_extract_task.task_definition_arn
    main_task_scheduled   = module.describe_vehicle_extract_task.schedule_enabled
    additional_task_deployed = var.deploy_additional_task
    environment           = var.environment
    region               = var.region
  }
}
