# Core Configuration
application             = "ais"
application_abbreviated = "ais"
service                 = "processing-apps"
region                  = "us-east-1"
region_abbreviated      = "ue1"
environment             = "nonprod"
component               = "processing-apps"
component_id            = "12345"

# Deployment Metadata
build_number  = "1.0.0"
launched_by   = "terraform"
launched_on   = "2024-01-01"
slack_contact = "#devops"

# Network Configuration
vpc_name      = "awsaaia3"
homenet_cidr  = "10.0.0.0/8"
ais_cidr      = "172.16.0.0/12"
remote_cidr   = "192.168.0.0/16"

# ECS Cluster Configuration
asg_ami              = "ami-0abcdef1234567890"  # Replace with actual ECS-optimized AMI ID
instance_type        = "m5.4xlarge"
asg_min_size         = 0
asg_max_size         = 10
asg_desired_capacity = 2

# EFS Configuration
efs_id             = "fs-0123456789abcdef0"  # Replace with actual EFS ID
efs_security_group = "sg-0123456789abcdef0"  # Replace with actual security group ID

# Lambda Configuration
package_path_ecs_termination_protection = "../../../lambda/ecs-termination-protection.zip"
lambda_role_arn                        = "arn:aws:iam::123456789012:role/lambda-execution-role"

# Auto Scaling Schedule Configuration
asg_scheduling_enabled = "true"
asg_scheduling_normal_map = {
  "nonprod.morning" = "0 8 * * MON-FRI"
  "nonprod.night"   = "0 20 * * MON-FRI"
  "default.morning" = "0 8 * * MON-FRI"
  "default.night"   = "0 20 * * MON-FRI"
}

# Task Configuration
task_role_arn      = "arn:aws:iam::123456789012:role/ecs-task-role"
execution_role_arn = "arn:aws:iam::123456789012:role/ecs-execution-role"

# Container Configuration
image_url_name_tag = "123456789012.dkr.ecr.us-east-1.amazonaws.com/describevehicleextract_dotnet_production:latest"
dotnet_env         = "Production"

# Application-specific Configuration
ini_bucket        = "my-config-bucket-nonprod"
rds_backup_bucket = "my-backup-bucket-nonprod"
rrri_topic_arn    = "arn:aws:sns:us-east-1:123456789012:rrri-topic"

# Scheduling Configuration
schedule_expression = "0 4 1 1 ? 2218"  # Disabled schedule - run manually for testing
task_enabled        = false
event_rule_arn      = "arn:aws:iam::123456789012:role/events-execution-role"
task_count          = 1

# Monitoring Configuration
alarm_action_arn            = "arn:aws:sns:us-east-1:123456789012:alerts"
alarm_metric_filter_pattern = "[timestamp, request_id, level=\"ERROR\", ...]"
log_retention_in_days       = 7

# Additional Task Configuration (Optional)
deploy_additional_task         = false
additional_image_url_name_tag  = "123456789012.dkr.ecr.us-east-1.amazonaws.com/additional-task:latest"
additional_schedule_expression = "rate(1 hour)"
additional_task_enabled        = false
