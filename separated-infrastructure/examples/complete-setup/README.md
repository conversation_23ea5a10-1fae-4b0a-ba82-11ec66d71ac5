# Complete ECS Infrastructure Setup Example

This example demonstrates how to use both the cluster infrastructure and task deployment modules together to create a complete ECS setup with proper separation of concerns.

## Overview

This example creates:
1. **ECS Cluster Infrastructure** - One-time setup including cluster, auto scaling, monitoring
2. **Task Deployment** - Application-specific task definitions and scheduling
3. **Optional Additional Tasks** - Shows how multiple applications can share the same cluster

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    AWS Account / Region                     │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │            ECS Cluster Infrastructure                   │ │
│  │  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐ │ │
│  │  │ ECS Cluster │  │ Auto Scaling │  │ Security Groups │ │ │
│  │  │             │  │ Group        │  │                 │ │ │
│  │  └─────────────┘  └──────────────┘  └─────────────────┘ │ │
│  │                                                         │ │
│  │  ┌─────────────┐  ┌──────────────┐  ┌─────────────────┐ │ │
│  │  │ CloudWatch  │  │ Lambda       │  │ CloudWatch      │ │ │
│  │  │ Alarms      │  │ Functions    │  │ Events          │ │ │
│  │  └─────────────┘  └──────────────┘  └─────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
│                              │                               │
│                              ▼                               │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Task Deployments                         │ │
│  │  ┌─────────────────────┐  ┌─────────────────────────────┐ │ │
│  │  │ DescribeVehicle     │  │ Additional Tasks            │ │ │
│  │  │ Extract Task        │  │ (Optional)                  │ │ │
│  │  │                     │  │                             │ │ │
│  │  │ • Task Definition   │  │ • Task Definition           │ │ │
│  │  │ • Log Groups        │  │ • Log Groups                │ │ │
│  │  │ • Scheduling        │  │ • Scheduling                │ │ │
│  │  │ • Monitoring        │  │ • Monitoring                │ │ │
│  │  └─────────────────────┘  └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Prerequisites

Before deploying this example, ensure you have:

1. **AWS CLI configured** with appropriate permissions
2. **Terraform installed** (version 0.14+)
3. **VPC and Subnets** already created and tagged appropriately
4. **EFS File System** created and accessible
5. **IAM Roles** created for ECS tasks, execution, Lambda, and EventBridge
6. **Container Images** pushed to ECR
7. **Lambda Package** for ECS termination protection

### Required IAM Roles

```bash
# ECS Task Role
arn:aws:iam::ACCOUNT:role/ecs-task-role

# ECS Execution Role  
arn:aws:iam::ACCOUNT:role/ecs-execution-role

# Lambda Execution Role
arn:aws:iam::ACCOUNT:role/lambda-execution-role

# EventBridge Execution Role
arn:aws:iam::ACCOUNT:role/events-execution-role
```

### Required AWS Resources

```bash
# VPC (tagged with Name)
vpc-********

# EFS File System
fs-0********9abcdef0

# ECR Repository with images
************.dkr.ecr.us-east-1.amazonaws.com/describevehicleextract_dotnet_production:latest

# SNS Topic for alerts
arn:aws:sns:us-east-1:************:alerts

# S3 Buckets
my-config-bucket-nonprod
my-backup-bucket-nonprod
```

## Usage

### Step 1: Configure Variables

1. Copy the example variables file:
   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```

2. Edit `terraform.tfvars` with your specific values:
   ```hcl
   # Update these with your actual values
   component_id = "your-component-id"
   asg_ami = "ami-your-ecs-optimized-ami"
   efs_id = "fs-your-efs-id"
   efs_security_group = "sg-your-efs-security-group"
   
   # IAM Role ARNs
   task_role_arn = "arn:aws:iam::YOUR-ACCOUNT:role/ecs-task-role"
   execution_role_arn = "arn:aws:iam::YOUR-ACCOUNT:role/ecs-execution-role"
   lambda_role_arn = "arn:aws:iam::YOUR-ACCOUNT:role/lambda-execution-role"
   event_rule_arn = "arn:aws:iam::YOUR-ACCOUNT:role/events-execution-role"
   
   # Container Image
   image_url_name_tag = "YOUR-ACCOUNT.dkr.ecr.us-east-1.amazonaws.com/your-image:latest"
   
   # S3 Buckets
   ini_bucket = "your-config-bucket"
   
   # SNS Topic
   alarm_action_arn = "arn:aws:sns:us-east-1:YOUR-ACCOUNT:alerts"
   ```

### Step 2: Initialize Terraform

```bash
terraform init
```

### Step 3: Plan the Deployment

```bash
terraform plan -var-file="terraform.tfvars"
```

### Step 4: Deploy the Infrastructure

```bash
terraform apply -var-file="terraform.tfvars"
```

This will create:
- ECS cluster with auto scaling
- Task definition for DescribeVehicleExtract
- CloudWatch log groups and alarms
- EventBridge rules for scheduling
- Lambda function for termination protection

### Step 5: Verify Deployment

```bash
# Check cluster status
aws ecs describe-clusters --clusters $(terraform output -raw cluster_name)

# Check task definition
aws ecs describe-task-definition --task-definition $(terraform output -raw task_definition_family)

# Check log groups
aws logs describe-log-groups --log-group-name-prefix $(terraform output -raw log_group_name)
```

## Deployment Scenarios

### Scenario 1: Basic Deployment

Deploy just the main task with minimal configuration:

```hcl
# In terraform.tfvars
deploy_additional_task = false
task_enabled = false  # Manual execution only
asg_scheduling_enabled = "false"  # No auto scaling schedule
```

### Scenario 2: Production Deployment

Deploy with full monitoring and scheduling:

```hcl
# In terraform.tfvars
deploy_additional_task = false
task_enabled = true
schedule_expression = "cron(0 4 * * ? *)"  # Daily at 4 AM
asg_scheduling_enabled = "true"
log_retention_in_days = 30
```

### Scenario 3: Multi-Task Deployment

Deploy multiple tasks to the same cluster:

```hcl
# In terraform.tfvars
deploy_additional_task = true
additional_task_enabled = true
additional_schedule_expression = "rate(1 hour)"
```

## Manual Task Execution

To run tasks manually:

```bash
# Get cluster and task definition ARNs
CLUSTER_ARN=$(terraform output -raw cluster_arn)
TASK_DEF_ARN=$(terraform output -raw task_definition_arn)

# Run the task
aws ecs run-task \
  --cluster $CLUSTER_ARN \
  --task-definition $TASK_DEF_ARN \
  --count 1
```

## Monitoring and Troubleshooting

### CloudWatch Logs

```bash
# View task logs
aws logs tail $(terraform output -raw log_group_name) --follow

# View specific log stream
aws logs describe-log-streams \
  --log-group-name $(terraform output -raw log_group_name)
```

### CloudWatch Alarms

```bash
# Check alarm status
aws cloudwatch describe-alarms \
  --alarm-names $(terraform output -raw failure_alarm_name)
```

### ECS Service Status

```bash
# List running tasks
aws ecs list-tasks --cluster $(terraform output -raw cluster_name)

# Describe task details
aws ecs describe-tasks \
  --cluster $(terraform output -raw cluster_name) \
  --tasks TASK-ID
```

## Scaling the Infrastructure

### Adding More Tasks

To add additional tasks to the same cluster:

1. Create new container definition files
2. Add new task modules in `main.tf`
3. Configure variables for the new tasks
4. Apply the changes

### Scaling the Cluster

To scale the ECS cluster:

```hcl
# In terraform.tfvars
asg_min_size = 1
asg_max_size = 20
asg_desired_capacity = 5
```

## Cost Optimization

### Auto Scaling Schedule

Configure auto scaling to reduce costs during off-hours:

```hcl
asg_scheduling_enabled = "true"
asg_scheduling_normal_map = {
  "nonprod.morning" = "0 8 * * MON-FRI"  # Scale up at 8 AM
  "nonprod.night"   = "0 20 * * MON-FRI" # Scale down at 8 PM
}
```

### Log Retention

Adjust log retention to balance observability and cost:

```hcl
log_retention_in_days = 7   # Development
log_retention_in_days = 30  # Production
```

## Security Considerations

1. **IAM Roles**: Use least-privilege principles
2. **Network Security**: Deploy in private subnets
3. **Secrets Management**: Use AWS Secrets Manager for sensitive data
4. **Container Security**: Regularly update base images
5. **Monitoring**: Enable CloudTrail and GuardDuty

## Cleanup

To destroy all resources:

```bash
terraform destroy -var-file="terraform.tfvars"
```

**Warning**: This will delete all resources including logs and data. Make sure to backup any important information first.

## Next Steps

1. **CI/CD Integration**: Integrate with your CI/CD pipeline
2. **Blue-Green Deployments**: Implement blue-green deployment strategy
3. **Service Discovery**: Add service discovery if needed
4. **Load Balancing**: Add Application Load Balancer for web services
5. **Database Integration**: Connect to RDS or other databases
6. **Secrets Management**: Integrate with AWS Secrets Manager

## Support

For issues or questions:
1. Check the individual module READMEs
2. Review AWS ECS documentation
3. Check Terraform AWS provider documentation
4. Review CloudWatch logs for specific errors
