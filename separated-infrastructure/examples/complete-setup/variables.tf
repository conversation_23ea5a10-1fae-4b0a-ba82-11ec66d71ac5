# Core Configuration Variables
variable "application" {
  description = "Application name"
  type        = string
  default     = "ais"
}

variable "application_abbreviated" {
  description = "Abbreviated application name"
  type        = string
  default     = "ais"
}

variable "service" {
  description = "Service name"
  type        = string
  default     = "processing-apps"
}

variable "region" {
  description = "AWS region"
  type        = string
  default     = "us-east-1"
}

variable "region_abbreviated" {
  description = "Abbreviated region name"
  type        = string
  default     = "ue1"
}

variable "environment" {
  description = "Environment (dev, test, prod, etc.)"
  type        = string
  default     = "nonprod"
}

variable "component" {
  description = "Component name"
  type        = string
  default     = "processing-apps"
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
  default     = "1.0.0"
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
  default     = "2024-01-01"
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
  default     = "#devops"
}

# Network Configuration
variable "vpc_name" {
  description = "Name of the VPC to use"
  type        = string
  default     = "awsaaia3"
}

variable "homenet_cidr" {
  description = "CIDR block for home network access"
  type        = string
  default     = "10.0.0.0/8"
}

variable "ais_cidr" {
  description = "CIDR block for AIS network access"
  type        = string
  default     = "**********/12"
}

variable "remote_cidr" {
  description = "CIDR block for remote network access"
  type        = string
  default     = "***********/16"
}

# ECS Cluster Configuration
variable "asg_ami" {
  description = "AMI ID for ECS instances"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type for ECS instances"
  type        = string
  default     = "m5.4xlarge"
}

variable "asg_min_size" {
  description = "Minimum size of the Auto Scaling Group"
  type        = number
  default     = 0
}

variable "asg_max_size" {
  description = "Maximum size of the Auto Scaling Group"
  type        = number
  default     = 10
}

variable "asg_desired_capacity" {
  description = "Desired capacity of the Auto Scaling Group"
  type        = number
  default     = 2
}

# EFS Configuration
variable "efs_id" {
  description = "EFS file system ID"
  type        = string
}

variable "efs_security_group" {
  description = "Security group ID for EFS access"
  type        = string
}

# Lambda Configuration
variable "package_path_ecs_termination_protection" {
  description = "Path to the ECS termination protection Lambda package"
  type        = string
}

variable "lambda_role_arn" {
  description = "ARN of the IAM role for Lambda function"
  type        = string
}

# Auto Scaling Schedule Configuration
variable "asg_scheduling_enabled" {
  description = "Enable/disable auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_scheduling_normal_map" {
  description = "Map of normal scheduling configurations"
  type        = map(string)
  default = {
    "nonprod.morning" = "0 8 * * MON-FRI"
    "nonprod.night"   = "0 20 * * MON-FRI"
    "default.morning" = "0 8 * * MON-FRI"
    "default.night"   = "0 20 * * MON-FRI"
  }
}

# Task Configuration
variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

variable "dotnet_env" {
  description = "DotNet environment configuration"
  type        = string
  default     = "Production"
}

# Application-specific Configuration
variable "ini_bucket" {
  description = "S3 bucket for configuration files"
  type        = string
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
  type        = string
  default     = ""
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression"
  type        = string
  default     = "0 4 1 1 ? 2218" # Disabled schedule
}

variable "task_enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = false
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks"
  type        = string
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
}

# Monitoring Configuration
variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered"
  type        = string
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
}

# Additional Task Configuration (Optional)
variable "deploy_additional_task" {
  description = "Whether to deploy an additional task to the same cluster"
  type        = bool
  default     = false
}

variable "additional_image_url_name_tag" {
  description = "Container image URL for additional task"
  type        = string
  default     = ""
}

variable "additional_schedule_expression" {
  description = "Schedule expression for additional task"
  type        = string
  default     = "rate(1 hour)"
}

variable "additional_task_enabled" {
  description = "Whether the additional scheduled task is enabled"
  type        = bool
  default     = false
}
