# Complete Example: ECS Cluster + Task Deployment
# This example shows how to use both modules together

terraform {
  required_providers {
    aws = {
      version = "~> 3.75"
      source  = "hashicorp/aws"
    }
  }
}

provider "aws" {
  region = var.region
}

# Step 1: Deploy the ECS Cluster Infrastructure
module "ecs_cluster" {
  source = "../../cluster-infrastructure"

  # Core Configuration
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                 = var.service
  region                  = var.region
  region_abbreviated      = var.region_abbreviated
  environment             = var.environment
  component               = var.component
  component_id            = var.component_id

  # Deployment Metadata
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact

  # Network Configuration
  vpc_name     = var.vpc_name
  homenet_cidr = var.homenet_cidr
  ais_cidr     = var.ais_cidr
  remote_cidr  = var.remote_cidr
  nfs_cidr     = var.nfs_cidr

  # ECS Configuration
  asg_ami              = var.asg_ami
  instance_type        = var.instance_type
  asg_min_size         = var.asg_min_size
  asg_max_size         = var.asg_max_size
  asg_desired_capacity = var.asg_desired_capacity

  # EFS Configuration
  efs_id             = var.efs_id
  efs_security_group = var.efs_security_group

  # Lambda Configuration
  package_path_ecs_termination_protection = var.package_path_ecs_termination_protection
  lambda_role_arn                         = var.lambda_role_arn
  account_type                            = var.account_type

  # Auto Scaling Schedule
  asg_scheduling_enabled    = var.asg_scheduling_enabled
  asg_scheduling_normal_map = var.asg_scheduling_normal_map
}

# Step 2: Deploy the Task Definition (using direct reference)
module "describe_vehicle_extract_task" {
  source = "../../task-deployment"

  # Core Configuration
  application  = var.application
  service      = var.service
  region       = var.region
  environment  = var.environment
  component    = var.component
  component_id = var.component_id

  # Deployment Metadata
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact

  # Cluster Configuration (using direct reference from cluster module)
  use_remote_state = false
  cluster_arn      = module.ecs_cluster.cluster_arn
  vpc_id           = module.ecs_cluster.vpc_id

  # Task Configuration
  task_friendly_name        = "US_DescribedVehicleExtract_DotNet"
  country_iso_code          = "US"
  container_definition_path = "./container-definitions/US_DescribedVehicleExtract_DotNet.json"

  # IAM Configuration
  task_role_arn      = var.task_role_arn
  execution_role_arn = var.execution_role_arn

  # Container Configuration
  image_url_name_tag = var.image_url_name_tag
  dotnet_env         = var.dotnet_env

  # Application-specific Configuration
  ini_bucket        = var.ini_bucket
  rds_backup_bucket = var.rds_backup_bucket
  rrri_topic_arn    = var.rrri_topic_arn

  # Scheduling Configuration
  schedule_expression = var.schedule_expression
  enabled             = var.task_enabled
  event_rule_arn      = var.event_rule_arn
  task_count          = var.task_count

  # Monitoring Configuration
  create_failure_alarm        = true
  alarm_action_arn            = var.alarm_action_arn
  alarm_description           = "Alarm for ${var.application} DescribedVehicleExtract task failures"
  alarm_metric_filter_pattern = var.alarm_metric_filter_pattern
  log_retention_in_days       = var.log_retention_in_days

  depends_on = [module.ecs_cluster]
}

# Optional: Deploy additional tasks to the same cluster
module "additional_task" {
  count  = var.deploy_additional_task ? 1 : 0
  source = "../../task-deployment"

  # Core Configuration
  application  = var.application
  service      = var.service
  region       = var.region
  environment  = var.environment
  component    = var.component
  component_id = var.component_id

  # Deployment Metadata
  build_number  = var.build_number
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact

  # Cluster Configuration (reusing the same cluster)
  use_remote_state = false
  cluster_arn      = module.ecs_cluster.cluster_arn
  vpc_id           = module.ecs_cluster.vpc_id

  # Task Configuration
  task_friendly_name        = "US_AdditionalTask_DotNet"
  country_iso_code          = "US"
  container_definition_path = "./container-definitions/additional-task.json"

  # IAM Configuration
  task_role_arn      = var.task_role_arn
  execution_role_arn = var.execution_role_arn

  # Container Configuration
  image_url_name_tag = var.additional_image_url_name_tag
  dotnet_env         = var.dotnet_env

  # Scheduling Configuration (different schedule)
  schedule_expression = var.additional_schedule_expression
  enabled             = var.additional_task_enabled
  event_rule_arn      = var.event_rule_arn

  # Monitoring Configuration
  create_failure_alarm = true
  alarm_action_arn     = var.alarm_action_arn

  depends_on = [module.ecs_cluster]
}
