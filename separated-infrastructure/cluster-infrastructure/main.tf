terraform {
  required_providers {
    aws = {
      version = "~> 3.75"
      source  = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.component_id
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# Data sources for existing infrastructure
data "aws_vpc" "vpc" {
  filter {
    name   = "tag:Name"
    values = [local.vpc_name]
  }
}

data "aws_subnet_ids" "private" {
  vpc_id = data.aws_vpc.vpc.id

  tags = {
    SUB-Type = "Private"
  }
}

data "aws_subnet_ids" "public" {
  vpc_id = data.aws_vpc.vpc.id

  tags = {
    SUB-Type = "Public"
  }
}

# Create the ECS Cluster
resource "aws_ecs_cluster" "main" {
  name = "${local.application}-${var.environment}-${var.component}-ecs"

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application  = local.application
    Environment  = var.environment
    Service      = local.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = local.slack_contact
    Name         = "ecs-${local.region_abbreviated}-${local.application_abbreviated}-${local.service}-${var.environment}-${var.build_number}"
    Component    = var.component
    component_id = var.component_id
  }
}

# Security group for EC2 instances in the ECS cluster
resource "aws_security_group" "ecs_instances" {
  name        = "${local.application}-${local.service}-${var.environment}-processing-apps-ec2"
  description = "Manages custom EC2 traffic for ECS cluster instances."
  vpc_id      = data.aws_vpc.vpc.id

  # Allow all outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow SSH from in-network
  ingress {
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [local.homenet_cidr, local.remote_cidr, local.ais_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = {
    Application  = local.application
    Environment  = var.environment
    Service      = local.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = local.slack_contact
    Name         = "sg-ec2-${local.region_abbreviated}-${local.application_abbreviated}-${local.service}-${var.environment}-${var.build_number}"
    Component    = var.component
    component_id = var.component_id
  }
}

# Launch configuration for ECS instances
resource "aws_launch_configuration" "ecs_instances" {
  name_prefix          = "${local.application}-${var.environment}-${var.component}-"
  image_id             = local.asg_ami
  instance_type        = local.instance_type
  security_groups      = [local.efs_security_group, aws_security_group.ecs_instances.id]
  user_data            = data.template_file.user_data.rendered
  iam_instance_profile = local.iam_instance_profile
  key_name             = local.ec2_key_name

  lifecycle {
    create_before_destroy = true
  }
}

# User data template for ECS instances
data "template_file" "user_data" {
  template = file("${path.module}/userdata.tpl")
  vars = {
    ecs_cluster_name = aws_ecs_cluster.main.name
    ecs_logging      = local.ecs_logging
    ec2_environment  = var.environment
    region           = var.region
    efs_id           = local.efs_id
  }
}

# Auto Scaling Group for ECS instances
resource "aws_autoscaling_group" "ecs_instances" {
  name                 = "${local.application}-${var.environment}-${var.component}-asg"
  launch_configuration = aws_launch_configuration.ecs_instances.name
  force_delete         = true

  min_size         = local.asg_min_size
  max_size         = local.asg_max_size
  desired_capacity = local.asg_desired_capacity

  wait_for_capacity_timeout = 0

  health_check_type         = "EC2"
  health_check_grace_period = 1000

  vpc_zone_identifier  = data.aws_subnet_ids.private.ids
  termination_policies = ["ClosestToNextInstanceHour", "Default"]
  enabled_metrics      = ["GroupMinSize", "GroupDesiredCapacity", "GroupInServiceInstances", "GroupPendingInstances", "GroupTerminatingInstances", "GroupTotalInstances"]

  tag {
    key                 = "Name"
    value               = "asg-${local.region_abbreviated}-${local.application_abbreviated}-${local.service}-${var.environment}-${var.build_number}"
    propagate_at_launch = true
  }

  tag {
    key                 = "Application"
    value               = local.application
    propagate_at_launch = true
  }

  tag {
    key                 = "Environment"
    value               = var.environment
    propagate_at_launch = true
  }

  tag {
    key                 = "Service"
    value               = local.service
    propagate_at_launch = true
  }

  tag {
    key                 = "Release"
    value               = var.build_number
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedBy"
    value               = var.launched_by
    propagate_at_launch = true
  }

  tag {
    key                 = "LaunchedOn"
    value               = var.launched_on
    propagate_at_launch = true
  }

  tag {
    key                 = "SlackContact"
    value               = local.slack_contact
    propagate_at_launch = true
  }

  tag {
    key                 = "Component"
    value               = var.component
    propagate_at_launch = true
  }

  tag {
    key                 = "ssm-patch-installation"
    value               = "true"
    propagate_at_launch = true
  }

  tag {
    key                 = "coxauto:ci-id"
    value               = var.component_id
    propagate_at_launch = true
  }

  lifecycle {
    create_before_destroy = true
  }
}

# Auto Scaling Policies
resource "aws_autoscaling_policy" "scale_out" {
  name                   = "${local.application}-${var.environment}-${var.component}-scale-out"
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = local.asp_scale_out_adjustment

  cooldown = local.asp_scale_out_cooldown
}

resource "aws_autoscaling_policy" "scale_in" {
  name                   = "${local.application}-${var.environment}-${var.component}-scale-in"
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name

  adjustment_type    = "ChangeInCapacity"
  scaling_adjustment = local.asp_scale_in_adjustment

  cooldown = local.asp_scale_in_cooldown
}

# Auto Scaling Schedules
resource "aws_autoscaling_schedule" "asg_schedule_normal_am" {
  count = local.asg_scheduling_enabled == "true" ? 1 : 0

  scheduled_action_name = "normal-morning-scale-up-${md5(
    lookup(
      local.asg_scheduling_normal_map,
      format("%s.morning", var.environment),
      local.asg_scheduling_normal_map["default.morning"],
    ),
  )}"
  min_size         = local.asg_min_size
  max_size         = local.asg_max_size
  desired_capacity = local.asg_desired_capacity
  recurrence = lookup(
    local.asg_scheduling_normal_map,
    format("%s.morning", var.environment),
    local.asg_scheduling_normal_map["default.morning"],
  )
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name
}

resource "aws_autoscaling_schedule" "asg_schedule_normal_pm" {
  count = local.asg_scheduling_enabled == "true" ? 1 : 0

  scheduled_action_name = "normal-nightly-scale-down-${md5(
    lookup(
      local.asg_scheduling_normal_map,
      format("%s.night", var.environment),
      local.asg_scheduling_normal_map["default.night"],
    ),
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    local.asg_scheduling_normal_map,
    format("%s.night", var.environment),
    local.asg_scheduling_normal_map["default.night"],
  )
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name
}

resource "aws_autoscaling_schedule" "asg_schedule_extended_am" {
  count = local.asg_extended_scheduling_enabled == "true" ? 1 : 0

  scheduled_action_name = "extended-morning-scale-up-${md5(
    lookup(
      local.asg_scheduling_extended_map,
      format("%s.morning", var.environment),
      local.asg_scheduling_extended_map["default.morning"]
    )
  )}"
  min_size         = local.asg_min_size
  max_size         = local.asg_max_size
  desired_capacity = local.asg_desired_capacity
  recurrence = lookup(
    local.asg_scheduling_extended_map,
    format("%s.morning", var.environment),
    local.asg_scheduling_extended_map["default.morning"]
  )
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name
}

resource "aws_autoscaling_schedule" "asg_schedule_extended_pm" {
  count = local.asg_extended_scheduling_enabled == "true" ? 1 : 0

  scheduled_action_name = "extended-nightly-scale-down-${md5(
    lookup(
      local.asg_scheduling_extended_map,
      format("%s.night", var.environment),
      local.asg_scheduling_extended_map["default.night"]
    )
  )}"
  min_size         = 0
  max_size         = 0
  desired_capacity = 0
  recurrence = lookup(
    local.asg_scheduling_extended_map,
    format("%s.night", var.environment),
    local.asg_scheduling_extended_map["default.night"]
  )
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name
}

# Lifecycle hook for termination protection
resource "aws_autoscaling_lifecycle_hook" "terminate_hook" {
  name                   = "${local.application}-${var.component}-${var.environment}-ecs-termination-protection-lifecyclehook-terminate"
  autoscaling_group_name = aws_autoscaling_group.ecs_instances.name
  default_result         = "CONTINUE"
  heartbeat_timeout      = 900
  lifecycle_transition   = "autoscaling:EC2_INSTANCE_TERMINATING"
}
