# ECS Cluster Infrastructure Module

This module creates and manages the core ECS cluster infrastructure that can be shared across multiple applications and task definitions. It's designed to be deployed once per environment and reused by multiple task deployment modules.

## Overview

This module provisions:
- **ECS Cluster**: The main container orchestration cluster
- **Auto Scaling Group**: EC2 instances that join the ECS cluster
- **Security Groups**: Network security for ECS instances
- **CloudWatch Alarms**: Monitoring and alerting for cluster health
- **Auto Scaling Policies**: Automatic scaling based on CPU/Memory utilization
- **Lambda Function**: ECS termination protection to ensure graceful task shutdown
- **CloudWatch Events**: Lifecycle event handling for instance termination

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    ECS Cluster Infrastructure               │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │ ECS Cluster │  │ Auto Scaling │  │ Security Groups     │ │
│  │             │  │ Group        │  │                     │ │
│  └─────────────┘  └──────────────┘  └─────────────────────┘ │
│                                                             │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │ CloudWatch  │  │ Lambda       │  │ CloudWatch Events   │ │
│  │ Alarms      │  │ Functions    │  │                     │ │
│  └─────────────┘  └──────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                Task Deployment Modules                     │
│  (Multiple applications can use the same cluster)          │
└─────────────────────────────────────────────────────────────┘
```

## Usage

### Basic Usage

```hcl
module "ecs_cluster" {
  source = "./cluster-infrastructure"

  # Core Configuration
  application             = "ais"
  application_abbreviated = "ais"
  service                 = "processing-apps"
  region                  = "us-east-1"
  region_abbreviated      = "ue1"
  environment             = "nonprod"
  component               = "processing-apps"
  component_id            = "12345"

  # Deployment Metadata
  build_number  = "1.0.0"
  launched_by   = "terraform"
  launched_on   = "2024-01-01"
  slack_contact = "#devops"

  # Network Configuration
  vpc_name      = "awsaaia3"
  homenet_cidr  = "10.0.0.0/8"
  ais_cidr      = "**********/12"
  remote_cidr   = "***********/16"

  # ECS Configuration
  asg_ami                = "ami-12345678"
  instance_type          = "m5.4xlarge"
  asg_min_size          = 0
  asg_max_size          = 10
  asg_desired_capacity  = 2

  # EFS Configuration
  efs_id             = "fs-12345678"
  efs_security_group = "sg-12345678"

  # Lambda Configuration
  package_path_ecs_termination_protection = "../lambda/ecs-termination-protection.zip"
  lambda_role_arn                        = "arn:aws:iam::123456789012:role/lambda-execution-role"

  # Auto Scaling Schedule (Optional)
  asg_scheduling_enabled = "true"
  asg_scheduling_normal_map = {
    "nonprod.morning" = "0 8 * * MON-FRI"
    "nonprod.night"   = "0 20 * * MON-FRI"
    "default.morning" = "0 8 * * MON-FRI"
    "default.night"   = "0 20 * * MON-FRI"
  }
}
```

### Advanced Configuration

```hcl
module "ecs_cluster" {
  source = "./cluster-infrastructure"

  # ... basic configuration ...

  # Custom CloudWatch Alarm Thresholds
  cw_alarm_high_cpu_threshold = 85
  cw_alarm_low_cpu_threshold  = 15
  cw_alarm_high_mem_threshold = 85
  cw_alarm_low_mem_threshold  = 15

  # Custom Auto Scaling Configuration
  asp_scale_out_adjustment = 2
  asp_scale_in_adjustment  = -1
  asp_scale_out_cooldown   = 300
  asp_scale_in_cooldown    = 600

  # Extended Scheduling
  asg_extended_scheduling_enabled = "true"
  asg_scheduling_extended_map = {
    "nonprod.morning" = "0 6 * * MON-FRI"
    "nonprod.night"   = "0 22 * * MON-FRI"
  }
}
```

## Outputs

This module provides the following outputs that can be used by task deployment modules:

### Core Cluster Information
- `cluster_name` - Name of the ECS cluster
- `cluster_arn` - ARN of the ECS cluster
- `cluster_id` - ID of the ECS cluster

### Network Information
- `vpc_id` - VPC ID where the cluster is deployed
- `private_subnet_ids` - List of private subnet IDs
- `public_subnet_ids` - List of public subnet IDs

### Security Groups
- `ecs_security_group_id` - Security group for ECS instances
- `efs_security_group_id` - Security group for EFS access

### Infrastructure Details
- `autoscaling_group_name` - Name of the Auto Scaling Group
- `lambda_function_arn` - ARN of the termination protection Lambda

## Integration with Task Deployment Module

Task deployment modules can reference this cluster infrastructure using Terraform remote state or by passing outputs directly:

```hcl
# In task deployment module
data "terraform_remote_state" "cluster" {
  backend = "s3"
  config = {
    bucket = "your-terraform-state-bucket"
    key    = "cluster-infrastructure/terraform.tfstate"
    region = "us-east-1"
  }
}

module "task_definition" {
  source = "../task-deployment"
  
  cluster_arn = data.terraform_remote_state.cluster.outputs.cluster_arn
  # ... other configuration
}
```

## Prerequisites

1. **VPC and Subnets**: The VPC and subnets must exist and be tagged appropriately
2. **EFS File System**: EFS must be created and accessible from the VPC
3. **IAM Roles**: Required IAM roles for ECS instances and Lambda function
4. **Lambda Package**: ECS termination protection Lambda function package

## Deployment

1. **Initialize Terraform**:
   ```bash
   terraform init
   ```

2. **Plan the deployment**:
   ```bash
   terraform plan -var-file="terraform.tfvars"
   ```

3. **Apply the configuration**:
   ```bash
   terraform apply -var-file="terraform.tfvars"
   ```

## Monitoring

The module creates several CloudWatch alarms for monitoring:
- **CPU Utilization**: High/Low CPU alarms with failsafe thresholds
- **Memory Utilization**: High/Low memory alarms with failsafe thresholds
- **Auto Scaling Events**: Lifecycle event monitoring

## Security

- EC2 instances are deployed in private subnets
- Security groups restrict access to necessary ports only
- EFS access is controlled via security groups
- Lambda function has minimal required permissions

## Maintenance

- **Scaling**: Adjust `asg_min_size`, `asg_max_size`, and `asg_desired_capacity` as needed
- **Instance Types**: Update `instance_type` for performance requirements
- **AMI Updates**: Regularly update `asg_ami` for security patches
- **Monitoring**: Review and adjust CloudWatch alarm thresholds based on usage patterns

## Troubleshooting

### Common Issues

1. **Instances not joining cluster**: Check ECS agent configuration in userdata
2. **Scaling issues**: Verify CloudWatch alarms and scaling policies
3. **Network connectivity**: Ensure security groups allow required traffic
4. **EFS mounting**: Verify EFS security group and network ACLs

### Logs

- **ECS Agent**: `/var/log/ecs/ecs-agent.log` on EC2 instances
- **CloudWatch**: Check CloudWatch Logs for Lambda function logs
- **Auto Scaling**: Review Auto Scaling Group activity history
