#==============================================================
# Computed Variables and Resolution Logic
# This file implements the variable precedence logic:
# Region-specific > Environment-specific > Global
#==============================================================

locals {
  # Determine account type from environment
  account_type = contains(["prod", "production"], lower(var.environment)) ? "prod" : "nonprod"
  
  # Get environment configuration
  environment_config = lookup(local.environment_configs, local.account_type, local.environment_configs["nonprod"])
  environment_resources = lookup(local.environment_resources, local.account_type, local.environment_resources["nonprod"])
  
  # Get region configuration with fallback
  region_config = lookup(
    lookup(local.region_configs, local.account_type, {}),
    var.region,
    lookup(local.region_configs["nonprod"], "us-east-1", {
      vpc_name    = "awsaaianp2"
      rr_vpc_name = "awsaaianp"
      nfs_cidr    = "100.71.40.0/22"
    })
  )
  
  #==============================================================
  # Resolved Configuration Values
  # These are the final computed values used by the infrastructure
  #==============================================================
  
  # Core application configuration
  application             = local.global_config.application
  application_abbreviated = local.global_config.application_abbreviated
  service                 = local.global_config.service
  slack_contact          = local.global_config.slack_contact
  
  # Region configuration
  region_abbreviated = lookup(local.region_config.abbreviations, var.region, "unknown")
  availability_zones = lookup(local.region_config.availability_zones, var.region, local.region_config.availability_zones["us-east-1"])
  
  # Network configuration (region-specific > global)
  vpc_name     = local.region_config.vpc_name
  rr_vpc_name  = local.region_config.rr_vpc_name
  nfs_cidr     = local.region_config.nfs_cidr
  homenet_cidr = local.global_config.homenet_cidr
  ais_cidr     = local.global_config.ais_cidr
  remote_cidr  = local.global_config.remote_cidr
  
  # Account-specific configuration
  account_id                  = local.environment_config.account_id
  account_name               = local.environment_config.account_name
  certificate_arn            = local.environment_config.certificate_arn
  hosted_zone_id             = local.environment_config.hosted_zone_id
  lambda_role_arn            = local.environment_config.lambda_role_arn
  internal_hosted_zone_id    = local.environment_config.internal_hosted_zone_id
  external_hosted_zone_id    = local.environment_config.external_hosted_zone_id
  external_domain            = local.environment_config.external_domain
  alb_logs_bucket           = local.environment_config.alb_logs_bucket
  alb_logs_enabled          = local.environment_config.alb_logs_enabled
  waf_request_limit         = local.environment_config.waf_request_limit
  
  # RDS configuration
  rds_db_root_user               = local.environment_config.rds_db_root_user
  rds_db_root_pw                = local.environment_config.rds_db_root_pw
  rds_multi_az                  = local.environment_config.rds_multi_az
  rds_backup_retention          = local.environment_config.rds_backup_retention
  rrri_db_cluster_instance_count = local.environment_config.rrri_db_cluster_instance_count
  rrri_db_instance_type         = local.environment_config.rrri_db_instance_type
  include_rrri_db_deploy        = local.environment_config.include_rrri_db_deploy
  incentives_cluster_replica_count    = local.environment_config.incentives_cluster_replica_count
  incentives_cluster_replica_count_ca = local.environment_config.incentives_cluster_replica_count_ca
  
  # Resource IDs (environment + region specific)
  efs_id = lookup(
    local.environment_resources.efs_shares,
    var.region,
    lookup(local.environment_resources.efs_shares, "us-east-1", "fs-5fcfafaa")
  )
  
  efs_security_group = lookup(
    local.environment_resources.efs_security_groups,
    var.region,
    lookup(local.environment_resources.efs_security_groups, "us-east-1", "sg-0358b7ef2c51fcbd1")
  )
  
  asg_ami = lookup(
    local.environment_resources.ecs_ami_ids,
    var.region,
    lookup(local.environment_resources.ecs_ami_ids, "us-east-1", "ami-0fe5f366c083f59ca")
  )
  
  webstack_ami_id = lookup(
    local.environment_resources.webstack_ami_ids,
    var.region,
    lookup(local.environment_resources.webstack_ami_ids, "us-east-1", "ami-0f2705ede1e949797")
  )
  
  package_bucket_name = lookup(
    local.environment_resources.package_buckets,
    var.region,
    lookup(local.environment_resources.package_buckets, "us-east-1", "ais.1-0.application.packages.np.ue1")
  )
  
  rds_backup_bucket_name = lookup(
    local.environment_resources.rds_backup_buckets,
    var.region,
    lookup(local.environment_resources.rds_backup_buckets, "us-east-1", "ais.1-0.rds.backups.np.ue1")
  )
  
  # ECS configuration with override support
  ecs_logging = local.ecs_defaults.logging
  instance_type = coalesce(var.instance_type, local.ecs_defaults.instance_type)
  iam_instance_profile = local.ecs_defaults.iam_instance_profile
  ec2_key_name = local.ecs_defaults.ec2_key_name
  
  # Auto Scaling configuration with override support
  asg_min_size = coalesce(var.asg_min_size, local.ecs_defaults.asg_min_size)
  asg_max_size = coalesce(var.asg_max_size, local.ecs_defaults.asg_max_size)
  asg_desired_capacity = coalesce(var.asg_desired_capacity, local.ecs_defaults.asg_desired_capacity)
  
  # Auto Scaling Policy configuration
  asp_scale_out_adjustment = local.ecs_defaults.scale_out_adjustment
  asp_scale_out_cooldown = local.ecs_defaults.scale_out_cooldown
  asp_scale_in_adjustment = local.ecs_defaults.scale_in_adjustment
  asp_scale_in_cooldown = local.ecs_defaults.scale_in_cooldown
  
  # Scheduling configuration with override support
  asg_scheduling_enabled = coalesce(var.asg_scheduling_enabled, local.ecs_defaults.scheduling_enabled)
  asg_extended_scheduling_enabled = coalesce(var.asg_extended_scheduling_enabled, local.ecs_defaults.extended_scheduling_enabled)
  
  # Schedule maps
  asg_scheduling_normal_map = local.autoscale_schedules.normal
  asg_scheduling_extended_map = local.autoscale_schedules.extended
  
  # CloudWatch Alarm configuration
  cw_alarm_low_cpu_threshold = local.cloudwatch_defaults.low_cpu_threshold
  cw_alarm_low_cpu_period = local.cloudwatch_defaults.low_cpu_period
  cw_alarm_low_cpu_evaluation_periods = local.cloudwatch_defaults.low_cpu_evaluation_periods
  cw_alarm_high_cpu_threshold = local.cloudwatch_defaults.high_cpu_threshold
  cw_alarm_high_cpu_period = local.cloudwatch_defaults.high_cpu_period
  cw_alarm_high_cpu_evaluation_periods = local.cloudwatch_defaults.high_cpu_evaluation_periods
  cw_alarm_high_cpu_datapoint = local.cloudwatch_defaults.high_cpu_datapoint
  cw_alarm_high_cpu_failsafe_threshold = local.cloudwatch_defaults.high_cpu_failsafe_threshold
  cw_alarm_high_cpu_failsafe_evaluation_periods = local.cloudwatch_defaults.high_cpu_failsafe_evaluation_periods
  cw_alarm_high_cpu_failsafe_datapoint = local.cloudwatch_defaults.high_cpu_failsafe_datapoint
  cw_alarm_low_mem_threshold = local.cloudwatch_defaults.low_mem_threshold
  cw_alarm_low_mem_period = local.cloudwatch_defaults.low_mem_period
  cw_alarm_low_mem_evaluation_periods = local.cloudwatch_defaults.low_mem_evaluation_periods
  cw_alarm_high_mem_threshold = local.cloudwatch_defaults.high_mem_threshold
  cw_alarm_high_mem_period = local.cloudwatch_defaults.high_mem_period
  cw_alarm_high_mem_evaluation_periods = local.cloudwatch_defaults.high_mem_evaluation_periods
  cw_alarm_high_mem_failsafe_threshold = local.cloudwatch_defaults.high_mem_failsafe_threshold
  cw_alarm_high_mem_failsafe_evaluation_periods = local.cloudwatch_defaults.high_mem_failsafe_evaluation_periods
}
