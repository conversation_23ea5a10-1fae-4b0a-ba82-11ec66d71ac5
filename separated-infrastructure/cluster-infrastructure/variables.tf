#==============================================================
# ECS Cluster Infrastructure Variables
# Implements original shared variable system precedence logic:
# Region-specific > Environment-specific > Global
#==============================================================

###############################################################
# Required Input Variables (from outside)
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
  type        = string
}

variable "build_number" {
  description = "Build Number"
  type        = string
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
}

variable "region" {
  description = "AWS Region"
  type        = string
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

# Deployment-specific variables
variable "package_path_ecs_termination_protection" {
  description = "Path to the ECS termination protection Lambda package"
  type        = string
}

###############################################################
# Global Variables (from shared-variables.global.tf)
###############################################################

variable "regions_abbreviated" {
  type = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }
}

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  type        = string
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "web"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  type        = string
  default     = "+ais-operations"
}

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network."
  type        = string
  default     = "************/32"
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network."
  type        = string
  default     = "***********/32"
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks."
  type        = string
  default     = "**************/32"
}

variable "ground_dc_cidrs" {
  description = "Private Subnet IDs for the provided VPC"
  type        = list(string)
  default     = ["************/32", "**************/32", "************/24"]
}

variable "internal_domain" {
  description = "Domain to use when creating internal dns records"
  type        = string
  default     = "ais-internal.com"
}

variable "s3_website_hosted_zone_id" {
  description = "The Amazon AWS hosted zone id for s3 website hosting"
  type        = map(string)
  default = {
    "us-east-1" = "Z3AQBSTGFYJSTF"
    "us-west-2" = "Z3BJ6K6RIION7M"
  }
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  type        = string
  default     = "CI0934608"
}

variable "internal_webstack_component_id" {
  description = "The Component ID of the internal-webstack"
  type        = string
  default     = "CI0941858"
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack"
  type        = string
  default     = "CI0941859"
}

###############################################################
# Environment-Specific Variables (nonprod/prod overrides)
###############################################################

# Account configuration maps
variable "account_config" {
  description = "Account-specific configuration"
  type = map(object({
    account_type = string
    account_id   = string
    account_name = string
    certificate_arn = string
    hosted_zone_id = string
    lambda_role_arn = string
    internal_hosted_zone_id = string
    external_hosted_zone_id = string
    external_domain = string
    alb_logs_bucket = string
    alb_logs_enabled = bool
    waf_request_limit = string
    rds_db_root_user = string
    rds_db_root_pw = string
    rds_multi_az = string
    rds_backup_retention = string
    rrri_db_cluster_instance_count = string
    rrri_db_instance_type = string
    include_rrri_db_deploy = bool
    incentives_cluster_replica_count = number
    incentives_cluster_replica_count_ca = number
  }))
  default = {
    "nonprod" = {
      account_type = "nonprod"
      account_id = "************"
      account_name = "awsaaianp"
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/278a49cb-11d5-4b49-937b-c79128597cf5"
      hosted_zone_id = "Z1VULNV75CDTF0"
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain = "coxais.com"
      alb_logs_bucket = "ais-nonprod-alb-logs"
      alb_logs_enabled = true
      waf_request_limit = "20000"
      rds_db_root_user = "aisadmin"
      rds_db_root_pw = "lightbomb"
      rds_multi_az = "false"
      rds_backup_retention = "7"
      rrri_db_cluster_instance_count = "1"
      rrri_db_instance_type = "db.r5.large"
      include_rrri_db_deploy = false
      incentives_cluster_replica_count = 0
      incentives_cluster_replica_count_ca = 0
    }
    "prod" = {
      account_type = "prod"
      account_id = "************"
      account_name = "awsaaiapd"
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/b8b5b5b5-1234-5678-9abc-************"
      hosted_zone_id = "Z1VULNV75CDTF0"
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain = "coxais.com"
      alb_logs_bucket = "ais-prod-alb-logs"
      alb_logs_enabled = true
      waf_request_limit = "50000"
      rds_db_root_user = "aisadmin"
      rds_db_root_pw = "lightbomb"
      rds_multi_az = "true"
      rds_backup_retention = "30"
      rrri_db_cluster_instance_count = "2"
      rrri_db_instance_type = "db.r5.xlarge"
      include_rrri_db_deploy = true
      incentives_cluster_replica_count = 1
      incentives_cluster_replica_count_ca = 1
    }
  }
}

# EFS shares by environment and region
variable "efs_shares" {
  description = "EFS shares used as general storage for the entire application"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "fs-5fcfafaa"
    }
    "prod" = {
      "us-east-1" = "fs-1234567890abcdef0"
      "us-west-2" = "fs-0987654321fedcba0"
    }
  }
}

# Package bucket names by environment and region
variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ais.1-0.application.packages.np.ue1"
    }
    "prod" = {
      "us-east-1" = "ais.1-0.application.packages.pd.ue1"
      "us-west-2" = "ais.1-0.application.packages.pd.uw2"
    }
  }
}

# EFS security groups by environment and region
variable "efs_security_group_map" {
  description = "The security group that has access to the efs"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "sg-0358b7ef2c51fcbd1"
    }
    "prod" = {
      "us-east-1" = "sg-1234567890abcdef0"
      "us-west-2" = "sg-0987654321fedcba0"
    }
  }
}

# Webstack AMI IDs by environment and region
variable "webstack_ami_ids" {
  description = "AMI IDs for the Consumer and Internal Webstack"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ami-0f2705ede1e949797"
    }
    "prod" = {
      "us-east-1" = "ami-1234567890abcdef0"
      "us-west-2" = "ami-0987654321fedcba0"
    }
  }
}

# ECS AMI IDs by environment and region
variable "ecs_ami_ids" {
  description = "Region specific AMI IDs for ECS"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ami-0fe5f366c083f59ca"
    }
    "prod" = {
      "us-east-1" = "ami-0fe5f366c083f59ca"
      "us-west-2" = "ami-0fe5f366c083f59ca"
    }
  }
}

# RDS backup bucket names by environment and region
variable "rds_backup_bucket_names" {
  description = "The S3 bucket that nightly rds backups store the dump to"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ais.1-0.rds.backups.np.ue1"
    }
    "prod" = {
      "us-east-1" = "ais.1-0.rds.backups.pd.ue1"
      "us-west-2" = "ais.1-0.rds.backups.pd.uw2"
    }
  }
}

###############################################################
# Region-Specific Variables
###############################################################

# VPC names by environment and region
variable "vpc_name_map" {
  description = "VPC Name by environment and region"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "awsaaianp2"
      "us-west-2" = "awsaaianp2"
    }
    "prod" = {
      "us-east-1" = "awsaaiapd2"
      "us-west-2" = "awsaaiapd2"
    }
  }
}

# RR VPC names by environment and region
variable "rr_vpc_name_map" {
  description = "Rates and Residuals VPC Name by environment and region"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "awsaaianp"
      "us-west-2" = "awsaaianp"
    }
    "prod" = {
      "us-east-1" = "awsaaiapd"
      "us-west-2" = "awsaaiapd"
    }
  }
}

# Availability zones by region
variable "availability_zones_map" {
  description = "Available Availability Zones by region"
  type = map(list(string))
  default = {
    "us-east-1" = ["us-east-1a", "us-east-1b", "us-east-1c"]
    "us-west-2" = ["us-west-2a", "us-west-2b", "us-west-2c"]
  }
}

# NFS CIDR by environment and region
variable "nfs_cidr_map" {
  description = "The CIDR block of the internal aws network (vpc) for NFS usage"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "***********/22"
      "us-west-2" = "***********/22"
    }
    "prod" = {
      "us-east-1" = "***********/22"
      "us-west-2" = "***********/22"
    }
  }
}

# RDS seed snapshot IDs by environment and region
variable "rds_seed_from_snapshot_ids" {
  description = "Most recent Snapshot Identifiers as optional seed data when creating RDS instances"
  type = map(map(map(string)))
  default = {
    "nonprod" = {
      "us-east-1" = {
        "incentives-us" = "arn:aws:rds:us-east-1:************:cluster-snapshot:incentives-us-master-for-nonprod-environments-20230615"
        "incentives-ca" = "arn:aws:rds:us-east-1:************:cluster-snapshot:incentives-ca-master-for-nonprod-environments-20230525"
        "mbasice"       = "arn:aws:rds:us-east-1:************:cluster-snapshot:mbasice-master-for-nonprod-environments-20240709"
      }
    }
    "prod" = {
      "us-east-1" = {}
      "us-west-2" = {}
    }
  }
}

###############################################################
# Autoscaling Schedule Variables
###############################################################

variable "autoscale_scheduling_normal_map" {
  description = "Cron map representation of autoscale scheduling for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "default.morning"   = "30 4 * * 1-5"
    "default.night"     = "30 15 * * *"
    "uat-alpha.morning" = "0 12 * * 1-5"
    "uat-alpha.night"   = "0 6 * * *"
    "uat-beta.morning"  = "0 4 * * 1-5"
    "uat-beta.night"    = "0 3 * * *"
    "uat-ext01.morning" = "0 9 * * 1-5"
    "uat-ext01.night"   = "0 3 * * *"
    "uat-ext02.morning" = "30 4 * * 1-5"
    "uat-ext02.night"   = "0 2 * * *"
  }
}

variable "autoscale_scheduling_extended_map" {
  description = "Cron map representation of autoscale scheduling for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "default.morning"   = ""
    "default.night"     = ""
    "uat-alpha.morning" = "58 11 1-2 * *"
    "uat-alpha.night"   = "2 6 2-3 * *"
    "uat-beta.morning"  = "58 3 1-2 * *"
    "uat-beta.night"    = "2 3 2-3 * *"
    "uat-ext01.morning" = "58 8 1-2 * *"
    "uat-ext01.night"   = "2 3 2-3 * *"
    "uat-ext02.morning" = "28 4 1-2 * *"
    "uat-ext02.night"   = "2 2 2-3 * *"
  }
}

variable "rds_scheduling_map" {
  description = "Map representing scheduling of RDS instances using Keep Stopped lambda"
  type        = map(string)
  default = {
    "default"   = "Mon-Fri 1000-2100 UTC+05:30"
    "uat-alpha" = "Mon-Fri 0600-2359 UTC-06:00/1-2 0600-2359 UTC-06:00"
    "uat-beta"  = "Mon-Fri 0000-2300 UTC-04:00/1-2 0000-2300 UTC-04:00"
    "uat-ext01" = "Mon-Fri 0500-2300 UTC-04:00/1-2 0500-2300 UTC-04:00"
    "uat-ext02" = "Mon-Fri 0030-2200 UTC-04:00/1-2 0030-2200 UTC-04:00"
  }
}

variable "rds_cluster_instance_class_kinds" {
  description = "Kind of RDS instance class that's assigned to a type of environment"
  type        = map(string)
  default = {
    "testing" = "db.r5.large"
    "staging" = "db.r5.xlarge"
  }
}

###############################################################
# ECS-Specific Variables
###############################################################

variable "ecs_logging" {
  description = "ECS logging drivers configuration"
  type        = string
  default     = "[\"json-file\",\"awslogs\"]"
}

variable "asg_min_size" {
  description = "Minimum size of the Auto Scaling Group"
  type        = number
  default     = 0
}

variable "asg_max_size" {
  description = "Maximum size of the Auto Scaling Group"
  type        = number
  default     = 10
}

variable "asg_desired_capacity" {
  description = "Desired capacity of the Auto Scaling Group"
  type        = number
  default     = 2
}

variable "instance_type" {
  description = "EC2 instance type for ECS instances"
  type        = string
  default     = "m5.4xlarge"
}

variable "iam_instance_profile" {
  description = "IAM instance profile for ECS instances"
  type        = string
  default     = "ais10-ec2-for-ec2ecs-role"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  type        = string
  default     = "AIS10"
}

# Auto Scaling Policy Configuration
variable "asp_scale_out_adjustment" {
  description = "Number of instances to add when scaling out"
  type        = number
  default     = 1
}

variable "asp_scale_out_cooldown" {
  description = "Cooldown period for scale out policy"
  type        = number
  default     = 300
}

variable "asp_scale_in_adjustment" {
  description = "Number of instances to remove when scaling in"
  type        = number
  default     = -1
}

variable "asp_scale_in_cooldown" {
  description = "Cooldown period for scale in policy"
  type        = number
  default     = 300
}

# Auto Scaling Schedule Configuration
variable "asg_scheduling_enabled" {
  description = "Enable/disable auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_extended_scheduling_enabled" {
  description = "Enable/disable extended auto scaling scheduling"
  type        = string
  default     = "false"
}

# CloudWatch Alarm Configuration - CPU
variable "cw_alarm_low_cpu_threshold" {
  description = "Low CPU threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_cpu_period" {
  description = "Period for low CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "Evaluation periods for low CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_threshold" {
  description = "High CPU threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_cpu_period" {
  description = "Period for high CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "Evaluation periods for high CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "Datapoints to alarm for high CPU"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "High CPU failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "Evaluation periods for high CPU failsafe alarm"
  type        = number
  default     = 1
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "Datapoints to alarm for high CPU failsafe"
  type        = number
  default     = 1
}

# CloudWatch Alarm Configuration - Memory
variable "cw_alarm_low_mem_threshold" {
  description = "Low memory threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_mem_period" {
  description = "Period for low memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "Evaluation periods for low memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_threshold" {
  description = "High memory threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_mem_period" {
  description = "Period for high memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "Evaluation periods for high memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "High memory failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "Evaluation periods for high memory failsafe alarm"
  type        = number
  default     = 1
}

###############################################################
# Variable Resolution Logic (Precedence: Region > Environment > Global)
###############################################################

locals {
  # Determine account type from environment
  account_type = contains(["prod", "production"], lower(var.environment)) ? "prod" : "nonprod"

  # Region abbreviation lookup
  region_abbreviated = lookup(var.regions_abbreviated, var.region, "unknown")

  # Account configuration resolution
  account_config = lookup(var.account_config, local.account_type, var.account_config["nonprod"])

  # Resolved variables with precedence logic
  vpc_name = lookup(
    lookup(var.vpc_name_map, local.account_type, {}),
    var.region,
    lookup(var.vpc_name_map["nonprod"], "us-east-1", "awsaaianp2")
  )

  rr_vpc_name = lookup(
    lookup(var.rr_vpc_name_map, local.account_type, {}),
    var.region,
    lookup(var.rr_vpc_name_map["nonprod"], "us-east-1", "awsaaianp")
  )

  availability_zones = lookup(var.availability_zones_map, var.region, var.availability_zones_map["us-east-1"])

  nfs_cidr = lookup(
    lookup(var.nfs_cidr_map, local.account_type, {}),
    var.region,
    lookup(var.nfs_cidr_map["nonprod"], "us-east-1", "***********/22")
  )

  efs_id = lookup(
    lookup(var.efs_shares, local.account_type, {}),
    var.region,
    lookup(var.efs_shares["nonprod"], "us-east-1", "fs-5fcfafaa")
  )

  efs_security_group = lookup(
    lookup(var.efs_security_group_map, local.account_type, {}),
    var.region,
    lookup(var.efs_security_group_map["nonprod"], "us-east-1", "sg-0358b7ef2c51fcbd1")
  )

  asg_ami = lookup(
    lookup(var.ecs_ami_ids, local.account_type, {}),
    var.region,
    lookup(var.ecs_ami_ids["nonprod"], "us-east-1", "ami-0fe5f366c083f59ca")
  )

  webstack_ami_id = lookup(
    lookup(var.webstack_ami_ids, local.account_type, {}),
    var.region,
    lookup(var.webstack_ami_ids["nonprod"], "us-east-1", "ami-0f2705ede1e949797")
  )

  package_bucket_name = lookup(
    lookup(var.package_bucket_names, local.account_type, {}),
    var.region,
    lookup(var.package_bucket_names["nonprod"], "us-east-1", "ais.1-0.application.packages.np.ue1")
  )

  rds_backup_bucket_name = lookup(
    lookup(var.rds_backup_bucket_names, local.account_type, {}),
    var.region,
    lookup(var.rds_backup_bucket_names["nonprod"], "us-east-1", "ais.1-0.rds.backups.np.ue1")
  )

  # Account-specific values
  account_id = local.account_config.account_id
  account_name = local.account_config.account_name
  certificate_arn = local.account_config.certificate_arn
  hosted_zone_id = local.account_config.hosted_zone_id
  lambda_role_arn = local.account_config.lambda_role_arn
  internal_hosted_zone_id = local.account_config.internal_hosted_zone_id
  external_hosted_zone_id = local.account_config.external_hosted_zone_id
  external_domain = local.account_config.external_domain
  alb_logs_bucket = local.account_config.alb_logs_bucket
  alb_logs_enabled = local.account_config.alb_logs_enabled
  waf_request_limit = local.account_config.waf_request_limit

  # RDS configuration
  rds_db_root_user = local.account_config.rds_db_root_user
  rds_db_root_pw = local.account_config.rds_db_root_pw
  rds_multi_az = local.account_config.rds_multi_az
  rds_backup_retention = local.account_config.rds_backup_retention
  rrri_db_cluster_instance_count = local.account_config.rrri_db_cluster_instance_count
  rrri_db_instance_type = local.account_config.rrri_db_instance_type
  include_rrri_db_deploy = local.account_config.include_rrri_db_deploy
  incentives_cluster_replica_count = local.account_config.incentives_cluster_replica_count
  incentives_cluster_replica_count_ca = local.account_config.incentives_cluster_replica_count_ca

  # Autoscaling schedule maps (use original variable references)
  asg_scheduling_normal_map = var.autoscale_scheduling_normal_map
  asg_scheduling_extended_map = var.autoscale_scheduling_extended_map
}
