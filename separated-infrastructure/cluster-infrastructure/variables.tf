# Core Infrastructure Variables
variable "application" {
  description = "Application name"
  type        = string
}

variable "application_abbreviated" {
  description = "Abbreviated application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "region_abbreviated" {
  description = "Abbreviated region name"
  type        = string
}

variable "environment" {
  description = "Environment (dev, test, prod, etc.)"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

# Network Configuration
variable "vpc_name" {
  description = "Name of the VPC to use"
  type        = string
}

variable "homenet_cidr" {
  description = "CIDR block for home network access"
  type        = string
}

variable "ais_cidr" {
  description = "CIDR block for AIS network access"
  type        = string
}

variable "remote_cidr" {
  description = "CIDR block for remote network access"
  type        = string
}

# ECS Configuration
variable "ecs_logging" {
  description = "ECS logging drivers configuration"
  type        = string
  default     = "[\"json-file\",\"awslogs\"]"
}

# Auto Scaling Group Configuration
variable "asg_min_size" {
  description = "Minimum size of the Auto Scaling Group"
  type        = number
  default     = 0
}

variable "asg_max_size" {
  description = "Maximum size of the Auto Scaling Group"
  type        = number
  default     = 10
}

variable "asg_desired_capacity" {
  description = "Desired capacity of the Auto Scaling Group"
  type        = number
  default     = 2
}

variable "asg_ami" {
  description = "AMI ID for ECS instances"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type for ECS instances"
  type        = string
  default     = "m5.4xlarge"
}

variable "iam_instance_profile" {
  description = "IAM instance profile for ECS instances"
  type        = string
  default     = "ais10-ec2-for-ec2ecs-role"
}

variable "ec2_key_name" {
  description = "Name of the key pair to associate with EC2 instances"
  type        = string
  default     = "AIS10"
}

# EFS Configuration
variable "efs_id" {
  description = "EFS file system ID"
  type        = string
}

variable "efs_security_group" {
  description = "Security group ID for EFS access"
  type        = string
}

# Auto Scaling Policy Configuration
variable "asp_scale_out_adjustment" {
  description = "Number of instances to add when scaling out"
  type        = number
  default     = 1
}

variable "asp_scale_out_cooldown" {
  description = "Cooldown period for scale out policy"
  type        = number
  default     = 300
}

variable "asp_scale_in_adjustment" {
  description = "Number of instances to remove when scaling in"
  type        = number
  default     = -1
}

variable "asp_scale_in_cooldown" {
  description = "Cooldown period for scale in policy"
  type        = number
  default     = 300
}

# Auto Scaling Schedule Configuration
variable "asg_scheduling_enabled" {
  description = "Enable/disable auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_scheduling_normal_map" {
  description = "Map of normal scheduling configurations"
  type        = map(string)
  default = {
    "default.morning" = "0 8 * * MON-FRI"
    "default.night"   = "0 20 * * MON-FRI"
  }
}

variable "asg_extended_scheduling_enabled" {
  description = "Enable/disable extended auto scaling scheduling"
  type        = string
  default     = "false"
}

variable "asg_scheduling_extended_map" {
  description = "Map of extended scheduling configurations"
  type        = map(string)
  default = {
    "default.morning" = "0 6 * * MON-FRI"
    "default.night"   = "0 22 * * MON-FRI"
  }
}

# CloudWatch Alarm Configuration - CPU
variable "cw_alarm_low_cpu_threshold" {
  description = "Low CPU threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_cpu_period" {
  description = "Period for low CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_cpu_evaluation_periods" {
  description = "Evaluation periods for low CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_threshold" {
  description = "High CPU threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_cpu_period" {
  description = "Period for high CPU alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_cpu_evaluation_periods" {
  description = "Evaluation periods for high CPU alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_datapoint" {
  description = "Datapoints to alarm for high CPU"
  type        = number
  default     = 2
}

variable "cw_alarm_high_cpu_failsafe_threshold" {
  description = "High CPU failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_cpu_failsafe_evaluation_periods" {
  description = "Evaluation periods for high CPU failsafe alarm"
  type        = number
  default     = 1
}

variable "cw_alarm_high_cpu_failsafe_datapoint" {
  description = "Datapoints to alarm for high CPU failsafe"
  type        = number
  default     = 1
}

# CloudWatch Alarm Configuration - Memory
variable "cw_alarm_low_mem_threshold" {
  description = "Low memory threshold for CloudWatch alarm"
  type        = number
  default     = 20
}

variable "cw_alarm_low_mem_period" {
  description = "Period for low memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_low_mem_evaluation_periods" {
  description = "Evaluation periods for low memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_threshold" {
  description = "High memory threshold for CloudWatch alarm"
  type        = number
  default     = 80
}

variable "cw_alarm_high_mem_period" {
  description = "Period for high memory alarm"
  type        = number
  default     = 300
}

variable "cw_alarm_high_mem_evaluation_periods" {
  description = "Evaluation periods for high memory alarm"
  type        = number
  default     = 2
}

variable "cw_alarm_high_mem_failsafe_threshold" {
  description = "High memory failsafe threshold"
  type        = number
  default     = 90
}

variable "cw_alarm_high_mem_failsafe_evaluation_periods" {
  description = "Evaluation periods for high memory failsafe alarm"
  type        = number
  default     = 1
}

# Lambda Configuration
variable "package_path_ecs_termination_protection" {
  description = "Path to the ECS termination protection Lambda package"
  type        = string
}

variable "lambda_role_arn" {
  description = "ARN of the IAM role for Lambda function"
  type        = string
}
