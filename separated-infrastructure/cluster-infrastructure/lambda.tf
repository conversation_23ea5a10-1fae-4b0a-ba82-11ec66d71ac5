# Lambda function for ECS termination protection
resource "aws_lambda_function" "ecs_termination_protection" {
  filename         = var.package_path_ecs_termination_protection
  function_name    = "${var.application}_${var.environment}_ecs_term_protection"
  description      = "Processes lifecycle events for ${var.application} ${var.environment} ec2s, check for the running task and delay the termination process until all the running task get completed."
  role             = var.lambda_role_arn
  handler          = "index.handler"
  runtime          = "nodejs18.x"
  source_code_hash = filebase64sha256(var.package_path_ecs_termination_protection)
  memory_size      = 128
  timeout          = 900

  environment {
    variables = {
      Environment = var.environment
    }
  }

  tags = {
    Application     = var.application
    Environment     = var.environment
    Component       = var.component
    Service         = var.service
    Release         = var.build_number
    LaunchedBy      = var.launched_by
    LaunchedOn      = var.launched_on
    SlackContact    = var.slack_contact
    Name            = "lm-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
    ApplicationPart = "ECSTerminationProtection"
  }
}

# CloudWatch Log Group for Lambda
resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${var.application}-${var.environment}-ecs-term-Protection"
  retention_in_days = 7
}

# CloudWatch Event Rule for Auto Scaling lifecycle events
resource "aws_cloudwatch_event_rule" "autoscaling_lifecycle" {
  name        = "${var.application}-${var.environment}-ecs-term-protection"
  description = "Capture autoscale lifecycle events for ${var.application} ${var.environment} and send them to lambda to handle"

  lifecycle {
    create_before_destroy = true
  }

  event_pattern = jsonencode({
    source      = ["aws.autoscaling"]
    detail-type = ["EC2 Instance-terminate Lifecycle Action"]
    detail = {
      AutoScalingGroupName = ["${var.application}-${var.environment}-${var.component}-asg"]
    }
  })
}

# CloudWatch Event Target to trigger Lambda
resource "aws_cloudwatch_event_target" "lambda_target" {
  rule = aws_cloudwatch_event_rule.autoscaling_lifecycle.name
  arn  = aws_lambda_function.ecs_termination_protection.arn
}

# Lambda permission to allow CloudWatch Events to invoke the function
resource "aws_lambda_permission" "allow_cloudwatch" {
  statement_id  = "${aws_cloudwatch_event_rule.autoscaling_lifecycle.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ecs_termination_protection.arn
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.autoscaling_lifecycle.arn
}
