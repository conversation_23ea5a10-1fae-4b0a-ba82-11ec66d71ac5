#!/bin/bash

# Update the system
yum update -y

# Install the ECS agent
yum install -y ecs-init
service docker start
chkconfig docker on

# Install CloudWatch agent
yum install -y awslogs

# Configure CloudWatch logs
cat > /etc/awslogs/awslogs.conf <<EOF
[general]
state_file = /var/lib/awslogs/agent-state

[/var/log/dmesg]
file = /var/log/dmesg
log_group_name = /aws/ec2/dmesg
log_stream_name = {instance_id}

[/var/log/messages]
file = /var/log/messages
log_group_name = /aws/ec2/messages
log_stream_name = {instance_id}
datetime_format = %b %d %H:%M:%S

[/var/log/docker]
file = /var/log/docker
log_group_name = /aws/ec2/docker
log_stream_name = {instance_id}
datetime_format = %Y-%m-%dT%H:%M:%S.%f
EOF

# Set the region for CloudWatch logs
sed -i -e "s/region = us-east-1/region = ${region}/g" /etc/awslogs/awscli.conf

# Start CloudWatch logs service
service awslogs start
chkconfig awslogs on

# Configure NTP
service chronyd start
chkconfig chronyd on

# Install SSM Agent
yum install -y https://s3.amazonaws.com/ec2-downloads-windows/SSMAgent/latest/linux_amd64/amazon-ssm-agent.rpm

# Setup CloudWatchMonitor for Memory Metrics
yum -y install python-pip
pip -q install cloudwatchmon
crontab -l | { cat; echo "* * * * * mon-put-instance-stats.py --mem-util --mem-used --mem-avail --disk-space-util --disk-path=/ --from-cron"; } | crontab -

# Mount aisdata for reference files
mkdir -p /aisdata/
chown 48:48 /aisdata/
echo "${efs_id}.efs.${region}.amazonaws.com:/ /aisdata nfs4 nfsvers=4.1,noresvport,rsize=1048576,wsize=1048576,hard,timeo=600,retrans=2 0 0" | tee -a /etc/fstab
mount -a -t nfs4

# Put the ecs.config file in the /etc/ecs folder to associate ec2 to the cluster
cat >/etc/ecs/ecs.config <<EOL
ECS_CLUSTER=${ecs_cluster_name}
ECS_AVAILABLE_LOGGING_DRIVERS=${ecs_logging}
ECS_ENABLE_TASK_IAM_ROLE=true
ECS_ENABLE_CONTAINER_METADATA=true
ECS_ENGINE_TASK_CLEANUP_WAIT_DURATION=10m
EOL

# Start the ECS agent
start ecs
