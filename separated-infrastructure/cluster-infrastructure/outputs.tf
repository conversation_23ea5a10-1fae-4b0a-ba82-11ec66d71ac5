# ECS Cluster Outputs
output "cluster_name" {
  description = "Name of the ECS cluster"
  value       = aws_ecs_cluster.main.name
}

output "cluster_arn" {
  description = "ARN of the ECS cluster"
  value       = aws_ecs_cluster.main.arn
}

output "cluster_id" {
  description = "ID of the ECS cluster"
  value       = aws_ecs_cluster.main.id
}

# Network Outputs
output "vpc_id" {
  description = "ID of the VPC"
  value       = data.aws_vpc.vpc.id
}

output "private_subnet_ids" {
  description = "List of private subnet IDs"
  value       = data.aws_subnet_ids.private.ids
}

output "public_subnet_ids" {
  description = "List of public subnet IDs"
  value       = data.aws_subnet_ids.public.ids
}

# Security Group Outputs
output "ecs_security_group_id" {
  description = "ID of the ECS instances security group"
  value       = aws_security_group.ecs_instances.id
}

output "efs_security_group_id" {
  description = "ID of the EFS security group"
  value       = local.efs_security_group
}

# Auto Scaling Group Outputs
output "autoscaling_group_name" {
  description = "Name of the Auto Scaling Group"
  value       = aws_autoscaling_group.ecs_instances.name
}

output "autoscaling_group_arn" {
  description = "ARN of the Auto Scaling Group"
  value       = aws_autoscaling_group.ecs_instances.arn
}

# Lambda Outputs
output "lambda_function_name" {
  description = "Name of the ECS termination protection Lambda function"
  value       = aws_lambda_function.ecs_termination_protection.function_name
}

output "lambda_function_arn" {
  description = "ARN of the ECS termination protection Lambda function"
  value       = aws_lambda_function.ecs_termination_protection.arn
}

# CloudWatch Outputs
output "cloudwatch_event_rule_name" {
  description = "Name of the CloudWatch event rule for lifecycle events"
  value       = aws_cloudwatch_event_rule.autoscaling_lifecycle.name
}

# EFS Outputs
output "efs_id" {
  description = "EFS file system ID"
  value       = local.efs_id
}

# Configuration Outputs for Task Deployment Module
output "region" {
  description = "AWS region"
  value       = var.region
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "application" {
  description = "Application name"
  value       = var.application
}

output "service" {
  description = "Service name"
  value       = var.service
}

output "component" {
  description = "Component name"
  value       = var.component
}
