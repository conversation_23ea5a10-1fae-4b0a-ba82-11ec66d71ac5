# CloudWatch Alarms for ECS Cluster Monitoring

# Low CPU Alarm
resource "aws_cloudwatch_metric_alarm" "low_cpu" {
  alarm_name = "${local.application}-${var.environment}-${var.component}-CPU-Low"

  alarm_description = "Triggered when CPU Reservation <= ${local.cw_alarm_low_cpu_threshold}% for ${local.cw_alarm_low_cpu_evaluation_periods} period(s) of ${local.cw_alarm_low_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = local.cw_alarm_low_cpu_threshold

  period             = local.cw_alarm_low_cpu_period
  evaluation_periods = local.cw_alarm_low_cpu_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_in.arn]
}

# High CPU Alarm
resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name        = "${local.application}-${var.environment}-${var.component}-CPU-High"
  alarm_description = "Triggered when CPU Reservation >= ${local.cw_alarm_high_cpu_threshold}% for ${local.cw_alarm_high_cpu_evaluation_periods} period(s) of ${local.cw_alarm_high_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = local.cw_alarm_high_cpu_threshold

  period              = local.cw_alarm_high_cpu_period
  evaluation_periods  = local.cw_alarm_high_cpu_evaluation_periods
  datapoints_to_alarm = local.cw_alarm_high_cpu_datapoint

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]
}

# High CPU Failsafe Alarm
resource "aws_cloudwatch_metric_alarm" "high_cpu_failsafe" {
  alarm_name        = "${local.application}-${var.environment}-${var.component}-CPU-High-Failsafe"
  alarm_description = "Failsafe Triggered when CPU Reservation >= ${local.cw_alarm_high_cpu_threshold}% for ${local.cw_alarm_high_cpu_failsafe_evaluation_periods} period(s) of ${local.cw_alarm_high_cpu_period} seconds."

  metric_name = "CPUReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = local.cw_alarm_high_cpu_failsafe_threshold

  period              = local.cw_alarm_high_cpu_period
  evaluation_periods  = local.cw_alarm_high_cpu_failsafe_evaluation_periods
  datapoints_to_alarm = local.cw_alarm_high_cpu_failsafe_datapoint

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]
}

# Low Memory Alarm
resource "aws_cloudwatch_metric_alarm" "low_mem" {
  alarm_name        = "${local.application}-${var.environment}-${var.component}-Mem-Low"
  alarm_description = "Triggered when ECS Memory Reservation <= ${local.cw_alarm_low_mem_threshold} for ${local.cw_alarm_low_mem_evaluation_periods} period(s) of ${local.cw_alarm_low_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "LessThanOrEqualToThreshold"
  threshold           = local.cw_alarm_low_mem_threshold

  period             = local.cw_alarm_low_mem_period
  evaluation_periods = local.cw_alarm_low_mem_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_in.arn]
}

# High Memory Alarm
resource "aws_cloudwatch_metric_alarm" "high_mem" {
  alarm_name        = "${local.application}-${var.environment}-${var.component}-Mem-High"
  alarm_description = "Triggered when Memory Reservation >= ${local.cw_alarm_high_mem_threshold} for ${local.cw_alarm_high_mem_evaluation_periods} period(s) of ${local.cw_alarm_high_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = local.cw_alarm_high_mem_threshold

  period             = local.cw_alarm_high_mem_period
  evaluation_periods = local.cw_alarm_high_mem_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]
}

# High Memory Failsafe Alarm
resource "aws_cloudwatch_metric_alarm" "high_mem_failsafe" {
  alarm_name        = "${local.application}-${var.environment}-${var.component}-Mem-High-Failsafe"
  alarm_description = "Failsafe Triggered when Memory Reservation >= ${local.cw_alarm_high_mem_failsafe_threshold} for ${local.cw_alarm_high_mem_evaluation_periods} period(s) of ${local.cw_alarm_high_mem_period} seconds."

  metric_name = "MemoryReservation"
  namespace   = "AWS/ECS"

  statistic           = "Average"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = local.cw_alarm_high_mem_failsafe_threshold

  period             = local.cw_alarm_high_mem_period
  evaluation_periods = local.cw_alarm_high_mem_failsafe_evaluation_periods

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]
}
