#==================================================================
# Shared Variables for Cross Regions in Non-Production Account
#==================================================================

variable "account_type" {
  description = "Type of AWS account"
  default     = "nonprod"
}

variable "account_id" {
  description = "ID of AWS account"
  default     = "************"
}

variable "account_name" {
  description = "Name of AWS account"
  default     = "awsaaianp"
}

variable "certificate_arn" {
  description = "SSL Cert to use for consumer webstack"
  default     = "arn:aws:acm:us-east-1:************:certificate/278a49cb-11d5-4b49-937b-c79128597cf5"
}

variable "hosted_zone_id" {
  description = "Route 53 HostedZoneID"
  default     = "Z1VULNV75CDTF0"
}

variable "lambda_role_arn" {
  description = "Role that the lambda should run under"
  default     = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
}

variable "efs_shares" {
  # WARNING: When you change EFS IDs, you must update the Consumer/Internal Webstack AMI which premounts it
  description = "EFS shares used as general storage for the entire application"
  type        = map(string)
  default = {
    "us-east-1" = "fs-5fcfafaa"
  }
}

variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.application.packages.np.ue1"
  }
}

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
  default     = "Z1VULNV75CDTF0"
}

variable "external_hosted_zone_id" {
  description = "AWS Id of the coxais.com hosted zone"
  default     = "Z11ZKVDNIDATM8"
}

variable "external_domain" {
  description = "AWS domain of the coxais.com hosted zone"
  default     = "coxais.com"
}

variable "efs_security_group" {
  description = "The security group that has access to the efs"
  type        = map(string)
  default = {
    "us-east-1" = "sg-0358b7ef2c51fcbd1"
  }
}

variable "webstack_ami_ids" {
  description = "AMI IDs for the Consumer and Internal Webstack (<#Ais10AmiDefs#>)"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0f2705ede1e949797"
  }
}

####https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-optimized_AMI.html
variable "ecs_ami_ids" {
  description = "Region specific AMI IDs for ECS (amzn2-ami-ecs-hvm-2.0.20200115-x86_64-ebs)"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0fe5f366c083f59ca"
  }
}

###############################################################
# Autoscaling Group Schedule Variables
###############################################################
# Active Periods (plus extra hour on each for DST): 
# UAT (Beta/Ext01): M-F or 1st/2nd of month @ 5am-10pm EST
# Scratch: M-F @ 10am-9pm UTC+5:30
# Non-Oxlo/India (default): M-F @ 7am-7pm EST
# UAT (Alpha): M-F or 1st/2nd of month @ 08AM - 01AM(next day) (EST)
# NOTE: In rds_scheduling_map to handle the next day (EST) scenario for UAT-Alpha, we used the UTC-06:00 timezone instead so that the range can lie into single day
###############################################################

variable "autoscale_scheduling_normal_map" {
  description = "Cron map representation of autoscale scheduling for normal business hour scale-up/down"
  type        = map(string)
  default = {
    "default.morning"   = "30 4 * * 1-5"
    "default.night"     = "30 15 * * *"
    "uat-alpha.morning" = "0 12 * * 1-5"
    "uat-alpha.night"   = "0 6 * * *"
    "uat-beta.morning"  = "0 4 * * 1-5"
    "uat-beta.night"    = "0 3 * * *"
    "uat-ext01.morning" = "0 9 * * 1-5"
    "uat-ext01.night"   = "0 3 * * *"
    "uat-ext02.morning" = "30 4 * * 1-5"
    "uat-ext02.night"   = "0 2 * * *"
  }
}

variable "autoscale_scheduling_extended_map" {
  description = "Cron map representation of autoscale scheduling for extended business hour scale-up/down on first/second of the month"
  type        = map(string)
  default = {
    "default.morning"   = ""
    "default.night"     = ""
    "uat-alpha.morning" = "58 11 1-2 * *"
    "uat-alpha.night"   = "2 6 2-3 * *"
    "uat-beta.morning"  = "58 3 1-2 * *"
    "uat-beta.night"    = "2 3 2-3 * *"
    "uat-ext01.morning" = "58 8 1-2 * *"
    "uat-ext01.night"   = "2 3 2-3 * *"
    "uat-ext02.morning" = "28 4 1-2 * *"
    "uat-ext02.night"   = "2 2 2-3 * *"
  }
}

variable "rds_scheduling_map" {
  description = "Map representing scheduling of RDS instances using Keep Stopped lamba"
  type        = map(string)
  default = {
    "default"   = "Mon-Fri 1000-2100 UTC+05:30"
    "uat-alpha" = "Mon-Fri 0600-2359 UTC-06:00/1-2 0600-2359 UTC-06:00"
    "uat-beta"  = "Mon-Fri 0000-2300 UTC-04:00/1-2 0000-2300 UTC-04:00"
    "uat-ext01" = "Mon-Fri 0500-2300 UTC-04:00/1-2 0500-2300 UTC-04:00"
    "uat-ext02" = "Mon-Fri 0030-2200 UTC-04:00/1-2 0030-2200 UTC-04:00"
  }
}

###############################################################
# RDS Variables
###############################################################

variable "rds_db_root_user" {
  description = "Username for the master DB user"
  default     = "aisadmin"
}

variable "rds_db_root_pw" {
  description = "Password for the master DB user. Note that this may show up in logs, and it will be stored in the state file."
  default     = "lightbomb"
}

variable "rds_multi_az" {
  description = "AZ for the RDS instance"
  default     = "false"
}

variable "rds_backup_bucket_names" {
  description = "The S3 bucket that nightly rds backups store the dump to."
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.rds.backups.np.ue1"
  }
}

variable "rds_backup_retention" {
  description = "The days to retain backups for. Must be 1 or greater to be a source for a Read Replica."
  default     = "7"
}

variable "rds_cluster_instance_class_kinds" {
  description = "Kind of RDS instance class that's assigned to a type of environment ('testing' value is default by allowing empty string)"
  type        = map(string)
  default = {
    "testing"    = "db.r5.large"
    "staging"    = "db.r5.xlarge"
  }
}

variable "rrri_db_cluster_instance_count" {
  description = "DB Cluster instance count (Pass more then 1 for multi AZ)"
  default     = "1"
}

variable "rrri_db_instance_type" {
  description = "Instance type of the database"
  default     = "db.r5.large"
}

variable "include_rrri_db_deploy" {
  description = "Determine if RRRI DB deployment is required. For non-production, it can be false"
  default     = false
}


variable "incentives_cluster_replica_count" {
  default = 0
  description = "DB Cluster instance count"
}

variable "incentives_cluster_replica_count_ca" {
  default = 0
  description = "DB Cluster instance count"
}


###############################################################
# ELB related Variables
###############################################################

variable "alb_logs_bucket" {
  description = "S3 bucket to store the ALB logs"
  default     = "ais-nonprod-alb-logs"
}

variable "alb_logs_enabled" {
  description = "Should enable the ALB logs"
  default     = true
}

variable "waf_request_limit" {
  description = "Request limit for nonprod WAF"
  default     = "20000"
}
