#==============================================================
# Core Input Variables
# These are the primary variables that must be provided when
# calling this module. All other variables are derived from these.
#==============================================================

variable "environment" {
  description = "Name of the environment we are building (nonprod, prod)"
  type        = string
  
  validation {
    condition = contains(["nonprod", "prod", "uat-alpha", "uat-beta", "uat-ext01", "uat-ext02"], var.environment)
    error_message = "Environment must be one of: nonprod, prod, uat-alpha, uat-beta, uat-ext01, uat-ext02."
  }
}

variable "region" {
  description = "AWS Region where resources will be deployed"
  type        = string
  
  validation {
    condition = contains(["us-east-1", "us-west-2"], var.region)
    error_message = "Region must be one of: us-east-1, us-west-2."
  }
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string
  default     = "processing-apps"
}

variable "component_id" {
  description = "Component ID for tagging and cost tracking"
  type        = string
}

#==============================================================
# Deployment Metadata Variables
# These track who deployed what and when
#==============================================================

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
}

#==============================================================
# Task Definition Configuration
# Core task configuration that must be provided
#==============================================================

variable "task_friendly_name" {
  description = "Friendly name for the task (used in resource naming)"
  type        = string
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

#==============================================================
# Cluster Configuration Variables
# Two ways to specify cluster: remote state or direct input
#==============================================================

variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state"
  type        = string
  default     = ""
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state"
  type        = string
  default     = ""
}

# Option 2: Direct Cluster Configuration
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false)"
  type        = string
  default     = ""
}

#==============================================================
# Optional Configuration Variables
# These have sensible defaults but can be overridden
#==============================================================

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = "US"
  
  validation {
    condition = length(var.country_iso_code) == 2
    error_message = "Country ISO code must be exactly 2 characters."
  }
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
  
  validation {
    condition = contains(["EC2", "FARGATE"], var.requires_compatibilities)
    error_message = "Requires compatibilities must be either 'EC2' or 'FARGATE'."
  }
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
  
  validation {
    condition = contains(["EC2", "FARGATE"], var.launch_type)
    error_message = "Launch type must be either 'EC2' or 'FARGATE'."
  }
}

variable "dotnet_env" {
  description = "DotNet environment configuration (defaults to environment if not specified)"
  type        = string
  default     = ""
}

# Application-specific Configuration (optional overrides)
variable "ini_bucket" {
  description = "S3 bucket for configuration files (uses package bucket if not specified)"
  type        = string
  default     = ""
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups (uses default if not specified)"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
  type        = string
  default     = ""
}

# Logging Configuration
variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
  
  validation {
    condition = contains([1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653], var.log_retention_in_days)
    error_message = "Log retention must be a valid CloudWatch Logs retention value."
  }
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = ""
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks"
  type        = string
  default     = ""
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
  
  validation {
    condition = var.task_count > 0 && var.task_count <= 10
    error_message = "Task count must be between 1 and 10."
  }
}

# Network Configuration (for Fargate tasks)
variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = false
}

# Monitoring and Alerting Configuration
variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = true
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm"
  type        = string
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = 1
  
  validation {
    condition = var.alarm_failure_threshold > 0
    error_message = "Alarm failure threshold must be greater than 0."
  }
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 1
  
  validation {
    condition = var.alarm_evaluation_periods > 0
    error_message = "Alarm evaluation periods must be greater than 0."
  }
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = 300
  
  validation {
    condition = var.alarm_period >= 60
    error_message = "Alarm period must be at least 60 seconds."
  }
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (e.g., SNS topic)"
  type        = string
  default     = ""
}
