# SSM Parameter Store Integration for ECS Task Deployment

# Local values for IAM policy generation (references locals from main.tf)
locals {
  # Separate parameters by type for IAM policy generation
  string_parameters = [
    for key, param in local.processed_ssm_parameters : param.name
    if param.type == "String" || param.type == "StringList"
  ]

  secure_string_parameters = [
    for key, param in local.processed_ssm_parameters : param.name
    if param.type == "SecureString"
  ]

  # Generate IAM policy statements for SSM parameter access
  ssm_policy_statements = var.enable_ssm_parameters ? [
    # Allow getting parameters
    {
      Effect = "Allow"
      Action = [
        "ssm:GetParameter",
        "ssm:GetParameters"
      ]
      Resource = [
        for param_name in concat(local.string_parameters, local.secure_string_parameters) :
        "arn:aws:ssm:${var.region}:*:parameter${param_name}"
      ]
    },
    # Allow KMS decryption for SecureString parameters
    length(local.secure_string_parameters) > 0 ? {
      Effect = "Allow"
      Action = [
        "kms:Decrypt"
      ]
      Resource = var.ssm_parameter_kms_key_id != "" ? [
        "arn:aws:kms:${var.region}:*:key/${var.ssm_parameter_kms_key_id}"
      ] : ["*"]
      Condition = {
        StringEquals = {
          "kms:ViaService" = "ssm.${var.region}.amazonaws.com"
        }
      }
    } : null
  ] : []

  # Filter out null policy statements
  filtered_ssm_policy_statements = [
    for statement in local.ssm_policy_statements : statement
    if statement != null
  ]
}

# Data source to validate SSM parameters exist (optional validation)
data "aws_ssm_parameter" "validation" {
  for_each = var.enable_ssm_parameters && var.validate_ssm_parameters ? local.processed_ssm_parameters : {}

  name = each.value.name
}

# IAM policy document for SSM parameter access
data "aws_iam_policy_document" "ssm_parameter_access" {
  count = var.enable_ssm_parameters && length(local.filtered_ssm_policy_statements) > 0 ? 1 : 0

  dynamic "statement" {
    for_each = local.filtered_ssm_policy_statements
    content {
      effect    = statement.value.Effect
      actions   = statement.value.Action
      resources = statement.value.Resource

      dynamic "condition" {
        for_each = lookup(statement.value, "Condition", null) != null ? [statement.value.Condition] : []
        content {
          test     = keys(condition.value)[0]
          variable = keys(condition.value[keys(condition.value)[0]])[0]
          values   = condition.value[keys(condition.value)[0]][keys(condition.value[keys(condition.value)[0]])[0]]
        }
      }
    }
  }
}

# IAM policy for SSM parameter access (to be attached to execution role)
resource "aws_iam_policy" "ssm_parameter_access" {
  count = var.enable_ssm_parameters && var.create_ssm_iam_policy && length(local.filtered_ssm_policy_statements) > 0 ? 1 : 0

  name_prefix = "${var.task_friendly_name}-${var.environment}-ssm-"
  description = "IAM policy for SSM parameter access for ${var.task_friendly_name} task"
  policy      = data.aws_iam_policy_document.ssm_parameter_access[0].json

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Component    = var.component
    TaskName     = var.task_friendly_name
  }
}

# Attach SSM policy to execution role (if policy is created and role attachment is enabled)
resource "aws_iam_role_policy_attachment" "ssm_parameter_access" {
  count = var.enable_ssm_parameters && var.create_ssm_iam_policy && var.attach_ssm_policy_to_execution_role && length(aws_iam_policy.ssm_parameter_access) > 0 ? 1 : 0

  role       = split("/", var.execution_role_arn)[1] # Extract role name from ARN
  policy_arn = aws_iam_policy.ssm_parameter_access[0].arn
}

# Output SSM parameter information for debugging and documentation
output "ssm_parameter_info" {
  description = "Information about configured SSM parameters"
  value = var.enable_ssm_parameters ? {
    base_path                = local.parameter_base_path
    naming_template          = local.parameter_naming_template
    processed_parameters     = local.processed_ssm_parameters
    string_parameters        = local.string_parameters
    secure_string_parameters = local.secure_string_parameters
    iam_policy_created       = length(aws_iam_policy.ssm_parameter_access) > 0
    iam_policy_arn           = length(aws_iam_policy.ssm_parameter_access) > 0 ? aws_iam_policy.ssm_parameter_access[0].arn : null
  } : null
}
