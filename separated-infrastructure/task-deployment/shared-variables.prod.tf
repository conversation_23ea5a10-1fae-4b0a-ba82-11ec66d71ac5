#==============================================================
# Shared Variables for Cross Regions in Production Account
#==============================================================

variable "account_type" {
  description = "Type of AWS account"
  default     = "prod"
}

variable "account_id" {
  description = "ID of AWS account"
  default     = "************"
}

variable "account_name" {
  description = "Name of AWS account"
  default     = "awsaaia"
}

variable "hosted_zone_id" {
  description = "Route 53 HostedZoneID"
  default     = "ZEFD6MLBXET7G"
}

variable "lambda_role_arn" {
  description = "Role that the lambda should run under"
  default     = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
}

//EFS Created by Manual process or Automation not known
variable "efs_shares" {
  # WARNING: When you change EFS IDs, you must update the Consumer/Internal Webstack AMI which premounts it
  description = "EFS shares used as general storage for the entire application"
  type        = map(string)
  default = {
    "us-east-1" = "fs-3a46ea72"
    "us-west-2" = "fs-09b0699719831c715"
  }
}

variable "efs_security_group" {
  description = "The security group that has access to the efs"
  type        = map(string)
  default = {
    "us-east-1" = "sg-cc81d485"
    "us-west-2" = "sg-06b44f90c47143f7e"
  }
}

variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.application.packages.ue1"
    "us-west-2" = "ais.1-0.application.packages.uw2"
  }
}

variable "internal_hosted_zone_id" {
  description = "AWS Id of the ais-internal hosted zone"
  default     = "ZEFD6MLBXET7G"
}

variable "external_hosted_zone_id" {
  description = "AWS Id of the coxautoratesincentives.com hosted zone"
  default     = "Z1TDOYE3C1D175"
}

variable "external_domain" {
  description = "AWS domain of the coxautoratesincentives.com hosted zone"
  default     = "coxautoratesincentives.com"
}

variable "webstack_ami_ids" {
  description = "AMI IDs for the Consumer and Internal Webstack (<#Ais10AmiDefs#> )"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0b83439163648ce59"
    "us-west-2" = "ami-0256451d6a80ee5c0"
  }
}

####https://docs.aws.amazon.com/AmazonECS/latest/developerguide/ecs-optimized_AMI.html
variable "ecs_ami_ids" {
  description = "Region specific AMI IDs for ECS (amzn2-ami-ecs-hvm-2.0.20200115-x86_64-ebs)"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0fe5f366c083f59ca"
    "us-west-2" = "ami-00b9dff790e659df2"
  }
}

###############################################################
# RDS Variables
###############################################################

variable "rds_db_root_user" {
  description = "Username for the master DB user"
  default     = "aisadmin"
}

variable "rds_db_root_pw" {
  description = "Password for the master DB user. Note that this may show up in logs, and it will be stored in the state file."
  default     = "n8PIWhrlyFG52inew"
}

variable "rds_multi_az" {
  description = "AZ for all RDS instances"
  default     = "true"
}

variable "rds_backup_bucket_names" {
  description = "The S3 bucket that nightly rds backups store the dump to."
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.rds.backups.ue1"
    "us-west-2" = "ais.1-0.rds.backups.uw2"
  }
}

variable "rds_backup_retention" {
  description = "The days to retain backups for. Must be 1 or greater to be a source for a Read Replica."
  default     = "30"
}

variable "rds_cluster_instance_class_kinds" {
  description = "Kind of RDS instance class that's assigned to a type of environment"
  type        = map(string)
  default = {
    "large"    = "db.r5.large"
    "xlarge"    = "db.r5.xlarge"
  }
}

variable "rrri_db_cluster_instance_count" {
  description = "DB Cluster instance count (Pass more then 1 for multi AZ)"
  default     = "2"
}

variable "rrri_db_instance_type" {
  description = "Instance type of the database"
  default     = "db.r5.large"
}

variable "include_rrri_db_deploy" {
  description = "Determine if RRRI DB deployment is required. For non-production, it can be false"
  default     = true
}


variable "incentives_cluster_replica_count" {
  default = 4
  description = "DB Cluster instance count"
}

variable "incentives_cluster_replica_count_ca" {
  default = 1
  description = "DB Cluster instance count"
}

###############################################################
# ELB related Variables
###############################################################


variable "alb_logs_bucket" {
  description = "S3 bucket to store the ALB logs"
  default     = "ais-production-alb-logs"
}

variable "alb_logs_enabled" {
  description = "Should enable the ALB logs"
  default     = true
}

variable "waf_request_limit" {
  description = "Request limit for prod WAF"
  default     = "20000"
}

