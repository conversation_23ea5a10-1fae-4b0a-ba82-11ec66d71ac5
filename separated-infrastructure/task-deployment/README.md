# ECS Task Deployment Module

This module creates and manages ECS task definitions and their associated resources for deploying applications to an existing ECS cluster. It's designed to work with the cluster infrastructure module and can be used to deploy multiple applications to the same cluster.

## Overview

This module provisions:
- **ECS Task Definition**: Container specifications and resource requirements
- **CloudWatch Log Group**: Centralized logging for task execution
- **EventBridge Rules**: Scheduled task execution (optional)
- **CloudWatch Alarms**: Monitoring and alerting for task failures
- **Log Metric Filters**: Error detection and alerting

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    ECS Cluster Infrastructure               │
│                    (Managed Separately)                     │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  Task Deployment Module                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │ Task        │  │ CloudWatch   │  │ EventBridge         │ │
│  │ Definition  │  │ Log Groups   │  │ Scheduling          │ │
│  └─────────────┘  └──────────────┘  └─────────────────────┘ │
│                                                             │
│  ┌─────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │ CloudWatch  │  │ Log Metric   │  │ Container           │ │
│  │ Alarms      │  │ Filters      │  │ Definitions         │ │
│  └─────────────┘  └──────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Usage

### Basic Usage with Remote State

```hcl
module "describe_vehicle_extract" {
  source = "./task-deployment"

  # Core Configuration
  application   = "ais"
  service       = "processing-apps"
  region        = "us-east-1"
  environment   = "nonprod"
  component     = "processing-apps"
  component_id  = "12345"

  # Deployment Metadata
  build_number  = "1.0.0"
  launched_by   = "terraform"
  launched_on   = "2024-01-01"
  slack_contact = "#devops"

  # Cluster Configuration (using remote state)
  use_remote_state      = true
  cluster_state_bucket  = "your-terraform-state-bucket"
  cluster_state_key     = "cluster-infrastructure/terraform.tfstate"

  # Task Configuration
  task_friendly_name        = "US_DescribedVehicleExtract_DotNet"
  country_iso_code          = "US"
  container_definition_path = "./container-definitions/sample-dotnet-task.json"
  
  # IAM Configuration
  task_role_arn      = "arn:aws:iam::123456789012:role/ecs-task-role"
  execution_role_arn = "arn:aws:iam::123456789012:role/ecs-execution-role"

  # Container Configuration
  image_url_name_tag = "123456789012.dkr.ecr.us-east-1.amazonaws.com/my-app:latest"
  dotnet_env         = "Production"

  # Application-specific Configuration
  ini_bucket        = "my-config-bucket"
  rds_backup_bucket = "my-backup-bucket"

  # Scheduling (optional)
  schedule_expression = "cron(0 4 * * ? *)"  # Daily at 4 AM
  enabled            = true
  event_rule_arn     = "arn:aws:iam::123456789012:role/events-execution-role"

  # Monitoring
  alarm_action_arn = "arn:aws:sns:us-east-1:123456789012:alerts"
}
```

### Usage with Direct Cluster Reference

```hcl
module "describe_vehicle_extract" {
  source = "./task-deployment"

  # ... basic configuration ...

  # Cluster Configuration (direct reference)
  use_remote_state = false
  cluster_arn      = "arn:aws:ecs:us-east-1:123456789012:cluster/my-cluster"
  vpc_id           = "vpc-12345678"

  # ... rest of configuration ...
}
```

### Advanced Configuration

```hcl
module "describe_vehicle_extract" {
  source = "./task-deployment"

  # ... basic configuration ...

  # Fargate Configuration
  launch_type             = "FARGATE"
  requires_compatibilities = "FARGATE"
  subnet_ids              = ["subnet-12345", "subnet-67890"]
  security_group_ids      = ["sg-12345678"]
  assign_public_ip        = false

  # Custom Logging
  log_retention_in_days = 30

  # Custom Monitoring
  create_failure_alarm         = true
  alarm_description           = "Custom alarm for task failures"
  alarm_metric_filter_pattern = "[timestamp, request_id, level=\"ERROR\", message]"
  alarm_failure_threshold     = 3
  alarm_evaluation_periods    = 2
  alarm_period               = 600

  # Multiple Task Instances
  task_count = 2
}
```

## Container Definitions

Container definitions are specified using JSON template files. The module supports variable substitution in these files.

### Sample Container Definition

Create a JSON file (e.g., `container-definitions/my-app.json`):

```json
[
    {
        "name": "${task_friendly_name}",
        "image": "${image_url_name_tag}",
        "cpu": 2048,
        "memory": 4096,
        "environment": [
            {"name": "ENVIRONMENT", "value": "${environment}"},
            {"name": "COUNTRY", "value": "${country_iso_code}"},
            {"name": "AWS_REGION", "value": "${region}"},
            {"name": "TASKNAME", "value": "${task_friendly_name}"},
            {"name": "DOTNET_ENVIRONMENT", "value": "${dotnet_env}"}
        ],
        "mountPoints": [
            {
                "sourceVolume": "aisdata",
                "containerPath": "/app/aisdata",
                "readonly": false
            }
        ],
        "logConfiguration": {
            "logDriver": "awslogs",
            "options": {
                "awslogs-group": "${task_friendly_name}_${environment}",
                "awslogs-region": "${region}",
                "awslogs-stream-prefix": "ecstasks"
            }
        },
        "essential": true
    }
]
```

### Available Template Variables

- `${task_friendly_name}` - Task name
- `${image_url_name_tag}` - Container image URL
- `${environment}` - Environment name
- `${country_iso_code}` - Country code
- `${region}` - AWS region
- `${dotnet_env}` - .NET environment
- `${ini_bucket}` - Configuration bucket
- `${rds_backup_bucket}` - Backup bucket
- `${rrri_topic_arn}` - SNS topic ARN

## Outputs

### Task Definition Information
- `task_definition_arn` - ARN of the task definition
- `task_definition_family` - Family name of the task definition
- `task_definition_revision` - Current revision number

### Logging Information
- `log_group_name` - CloudWatch log group name
- `log_group_arn` - CloudWatch log group ARN

### Scheduling Information
- `scheduled_rule_name` - EventBridge rule name (if scheduling enabled)
- `scheduled_rule_arn` - EventBridge rule ARN (if scheduling enabled)
- `schedule_enabled` - Whether scheduling is active

### Monitoring Information
- `failure_alarm_name` - CloudWatch alarm name (if created)
- `failure_alarm_arn` - CloudWatch alarm ARN (if created)

## Integration Patterns

### 1. Using Remote State (Recommended)

```hcl
# Deploy cluster infrastructure first
module "cluster" {
  source = "./cluster-infrastructure"
  # ... cluster configuration
}

# Deploy task using remote state reference
module "task" {
  source = "./task-deployment"
  
  use_remote_state     = true
  cluster_state_bucket = "my-terraform-state"
  cluster_state_key    = "cluster/terraform.tfstate"
  
  # ... task configuration
}
```

### 2. Using Direct References

```hcl
# Deploy cluster infrastructure
module "cluster" {
  source = "./cluster-infrastructure"
  # ... cluster configuration
}

# Deploy task using direct references
module "task" {
  source = "./task-deployment"
  
  use_remote_state = false
  cluster_arn      = module.cluster.cluster_arn
  vpc_id           = module.cluster.vpc_id
  
  # ... task configuration
}
```

### 3. Multiple Tasks on Same Cluster

```hcl
# Task 1
module "task_1" {
  source = "./task-deployment"
  
  task_friendly_name = "Task1"
  # ... configuration
}

# Task 2
module "task_2" {
  source = "./task-deployment"
  
  task_friendly_name = "Task2"
  # ... configuration
}
```

## Scheduling

The module supports EventBridge (CloudWatch Events) scheduling:

### Schedule Expressions

- **Cron**: `cron(0 4 * * ? *)` - Daily at 4 AM UTC
- **Rate**: `rate(1 hour)` - Every hour
- **Disabled**: `""` - No scheduling

### Manual Execution

Tasks can also be run manually using AWS CLI:

```bash
aws ecs run-task \
  --cluster my-cluster \
  --task-definition my-task:1 \
  --count 1
```

## Monitoring and Alerting

### CloudWatch Alarms

The module creates alarms based on log patterns:

- **Error Detection**: Monitors logs for error patterns
- **Failure Threshold**: Configurable failure count
- **SNS Integration**: Sends alerts to specified topics

### Log Metric Filters

Automatically creates metric filters to detect:
- Application errors
- Task failures
- Custom patterns (configurable)

## Troubleshooting

### Common Issues

1. **Task Won't Start**
   - Check IAM roles and permissions
   - Verify container image exists and is accessible
   - Check resource requirements (CPU/memory)

2. **Scheduling Not Working**
   - Verify EventBridge rule is enabled
   - Check IAM role for EventBridge
   - Confirm schedule expression syntax

3. **Logs Not Appearing**
   - Verify CloudWatch log group exists
   - Check log driver configuration
   - Ensure proper IAM permissions

### Debugging Commands

```bash
# Check task definition
aws ecs describe-task-definition --task-definition my-task

# View task execution
aws ecs list-tasks --cluster my-cluster

# Check logs
aws logs describe-log-groups --log-group-name-prefix my-task

# View EventBridge rules
aws events list-rules --name-prefix my-task
```

## Best Practices

1. **Resource Management**
   - Set appropriate CPU and memory limits
   - Use health checks for long-running tasks
   - Configure proper log retention

2. **Security**
   - Use least-privilege IAM roles
   - Store secrets in AWS Secrets Manager
   - Use private subnets for Fargate tasks

3. **Monitoring**
   - Enable CloudWatch alarms
   - Use structured logging
   - Set up proper alerting channels

4. **Deployment**
   - Use blue-green deployments for updates
   - Test in non-production environments first
   - Monitor deployment metrics
