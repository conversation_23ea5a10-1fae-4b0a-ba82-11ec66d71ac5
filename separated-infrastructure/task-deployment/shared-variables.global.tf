#==============================================================
# Shared Variables for All Accounts
#==============================================================

###############################################################
# Lookup Dictionaries
###############################################################

variable "regions_abbreviated" {
  type = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }
}

###############################################################
# Required Variables from Outside
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
}

variable "build_number" {
  description = "Build Number"
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
}

variable "region" {
  description = "AWS Region"
}

variable "component" {
  description = "Name of the component being deployed"
}

###############################################################
# Application Variables
###############################################################

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  default     = "web" # May want to start varying these in modules since so much infrastructure
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  default     = "+ais-operations"
}

###############################################################
# VPC Variables
###############################################################

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network."
  default     = "************/32"
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network."
  default     = "***********/32"
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks."
  default     = "**************/32"
}

variable "ground_dc_cidrs" {
  description = "Private Subnet IDs for the provided VPC"
  type        = list(string)
  default     = ["************/32", "**************/32", "************/24"]
}

###############################################################
# Route 53 Variables
###############################################################

variable "internal_domain" {
  description = "Domain to use when creating internal dns records"
  default     = "ais-internal.com"
}

variable "s3_website_hosted_zone_id" {
  description = "The Amazon AWS hosted zone id for s3 website hosting https://docs.aws.amazon.com/general/latest/gr/rande.html#s3_website_region_endpoints"
  type        = map(string)
  default = {
    "us-east-1" = "Z3AQBSTGFYJSTF"
    "us-west-2" = "Z3BJ6K6RIION7M"
  }
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  default     = "CI0934608"
}

variable "internal_webstack_component_id" {
  description = "The Component ID of the internal-webstack"
  default     = "CI0941858"
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack"
  default     = "CI0941859"
}
