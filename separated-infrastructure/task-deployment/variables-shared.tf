#==============================================================
# Shared Configuration Variables
# These are the same configurations used by cluster-infrastructure
# to ensure consistency across modules
#==============================================================

locals {
  # Global application configuration (same as cluster-infrastructure)
  global_config = {
    # Application Identity
    application             = "ais10"
    application_abbreviated = "a10"
    service                 = "web"
    slack_contact          = "+ais-operations"
    
    # Network Configuration
    homenet_cidr = "204.11.136.0/32"
    ais_cidr     = "107.1.95.66/32"
    remote_cidr  = "108.252.235.16/32"
    
    # Ground DC CIDRs
    ground_dc_cidrs = ["98.175.77.34/32", "204.193.152.32/32", "10.224.239.0/24"]
    
    # DNS Configuration
    internal_domain = "ais-internal.com"
    
    # Component IDs
    processing_apps_component_id    = "CI0934608"
    internal_webstack_component_id  = "CI0941858"
    consumer_webstack_component_id  = "CI0941859"
  }
  
  # Region mappings (same as cluster-infrastructure)
  region_config = {
    abbreviations = {
      "us-east-1" = "ue1"
      "us-west-2" = "uw2"
    }
    
    availability_zones = {
      "us-east-1" = ["us-east-1a", "us-east-1b", "us-east-1c"]
      "us-west-2" = ["us-west-2a", "us-west-2b", "us-west-2c"]
    }
    
    s3_website_hosted_zone_ids = {
      "us-east-1" = "Z3AQBSTGFYJSTF"
      "us-west-2" = "Z3BJ6K6RIION7M"
    }
  }
  
  # Environment-specific account configurations (same as cluster-infrastructure)
  environment_configs = {
    nonprod = {
      # Account Information
      account_type = "nonprod"
      account_id   = "************"
      account_name = "awsaaianp"
      
      # SSL/TLS Configuration
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/278a49cb-11d5-4b49-937b-c79128597cf5"
      
      # DNS Configuration
      hosted_zone_id          = "Z1VULNV75CDTF0"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain         = "coxais.com"
      
      # IAM Configuration
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      
      # Load Balancer Configuration
      alb_logs_bucket  = "ais-nonprod-alb-logs"
      alb_logs_enabled = true
      
      # WAF Configuration
      waf_request_limit = "20000"
      
      # RDS Configuration
      rds_db_root_user    = "aisadmin"
      rds_db_root_pw      = "lightbomb"
      rds_multi_az        = "false"
      rds_backup_retention = "7"
      
      # RRRI Database Configuration
      rrri_db_cluster_instance_count = "1"
      rrri_db_instance_type         = "db.r5.large"
      include_rrri_db_deploy        = false
      
      # Incentives Configuration
      incentives_cluster_replica_count    = 0
      incentives_cluster_replica_count_ca = 0
    }
    
    prod = {
      # Account Information
      account_type = "prod"
      account_id   = "************"
      account_name = "awsaaiapd"
      
      # SSL/TLS Configuration
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/b8b5b5b5-1234-5678-9abc-************"
      
      # DNS Configuration
      hosted_zone_id          = "Z1VULNV75CDTF0"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain         = "coxais.com"
      
      # IAM Configuration
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      
      # Load Balancer Configuration
      alb_logs_bucket  = "ais-prod-alb-logs"
      alb_logs_enabled = true
      
      # WAF Configuration
      waf_request_limit = "50000"
      
      # RDS Configuration
      rds_db_root_user    = "aisadmin"
      rds_db_root_pw      = "lightbomb"
      rds_multi_az        = "true"
      rds_backup_retention = "30"
      
      # RRRI Database Configuration
      rrri_db_cluster_instance_count = "2"
      rrri_db_instance_type         = "db.r5.xlarge"
      include_rrri_db_deploy        = true
      
      # Incentives Configuration
      incentives_cluster_replica_count    = 1
      incentives_cluster_replica_count_ca = 1
    }
  }
  
  # Environment-specific resource configurations (same as cluster-infrastructure)
  environment_resources = {
    nonprod = {
      # S3 Buckets
      package_buckets = {
        "us-east-1" = "ais.1-0.application.packages.np.ue1"
      }
      
      rds_backup_buckets = {
        "us-east-1" = "ais.1-0.rds.backups.np.ue1"
      }
    }
    
    prod = {
      # S3 Buckets
      package_buckets = {
        "us-east-1" = "ais.1-0.application.packages.pd.ue1"
        "us-west-2" = "ais.1-0.application.packages.pd.uw2"
      }
      
      rds_backup_buckets = {
        "us-east-1" = "ais.1-0.rds.backups.pd.ue1"
        "us-west-2" = "ais.1-0.rds.backups.pd.uw2"
      }
    }
  }
}
