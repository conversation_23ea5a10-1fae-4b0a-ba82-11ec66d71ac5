# Core Configuration Variables
variable "application" {
  description = "Application name"
  type        = string
}

variable "service" {
  description = "Service name"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
}

variable "environment" {
  description = "Environment (dev, test, prod, etc.)"
  type        = string
}

variable "component" {
  description = "Component name"
  type        = string
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

variable "build_number" {
  description = "Build number for deployment tracking"
  type        = string
}

variable "launched_by" {
  description = "Who launched this deployment"
  type        = string
}

variable "launched_on" {
  description = "When this deployment was launched"
  type        = string
}

variable "slack_contact" {
  description = "Slack contact for notifications"
  type        = string
}

# Cluster Configuration - Two ways to specify cluster
variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state"
  type        = string
  default     = ""
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state"
  type        = string
  default     = ""
}

# Option 2: Direct Cluster Configuration
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false)"
  type        = string
  default     = ""
}

# Task Definition Configuration
variable "task_friendly_name" {
  description = "Friendly name for the task"
  type        = string
}

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = "US"
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

# Container Configuration
variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

variable "dotnet_env" {
  description = "DotNet environment configuration"
  type        = string
  default     = ""
}

# Application-specific Configuration
variable "ini_bucket" {
  description = "S3 bucket for configuration files"
  type        = string
  default     = ""
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
  type        = string
  default     = ""
}

# Logging Configuration
variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = ""
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks"
  type        = string
  default     = ""
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
}

# Network Configuration (for Fargate tasks)
variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = false
}

# Monitoring and Alerting Configuration
variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = true
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm"
  type        = string
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = 1
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 1
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = 300
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (e.g., SNS topic)"
  type        = string
  default     = ""
}