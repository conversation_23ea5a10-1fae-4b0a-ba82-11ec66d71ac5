#==============================================================
# ECS Task Deployment Variables
# Implements original shared variable system precedence logic:
# Region-specific > Environment-specific > Global
#==============================================================

###############################################################
# Required Input Variables (from outside)
###############################################################

variable "environment" {
  description = "Name of the environment we are building"
  type        = string
}

variable "build_number" {
  description = "Build Number"
  type        = string
}

variable "launched_by" {
  description = "Name of the user or tool that launched this environment"
  type        = string
}

variable "launched_on" {
  description = "Timestamp representing when the environment was last built"
  type        = string
}

variable "region" {
  description = "AWS Region"
  type        = string
}

variable "component" {
  description = "Name of the component being deployed"
  type        = string
}

variable "component_id" {
  description = "Component ID for tagging"
  type        = string
}

###############################################################
# Global Variables (from shared-variables.global.tf)
###############################################################

variable "regions_abbreviated" {
  type = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }
}

variable "application" {
  description = "Name of the application to be used when tagging aws resources"
  type        = string
  default     = "ais10"
}

variable "application_abbreviated" {
  description = "Abbreviation for Application name"
  type        = string
  default     = "a10"
}

variable "service" {
  description = "The service that this configuration builds"
  type        = string
  default     = "web"
}

variable "slack_contact" {
  description = "Slack channel that should be notified by the various aws monkeys"
  type        = string
  default     = "+ais-operations"
}

variable "homenet_cidr" {
  description = "The public CIDR block of the HomeNet network."
  type        = string
  default     = "************/32"
}

variable "ais_cidr" {
  description = "The public CIDR block of the AIS network."
  type        = string
  default     = "***********/32"
}

variable "remote_cidr" {
  description = "The public CIDR block of the Remote networks."
  type        = string
  default     = "**************/32"
}

variable "ground_dc_cidrs" {
  description = "Private Subnet IDs for the provided VPC"
  type        = list(string)
  default     = ["************/32", "**************/32", "************/24"]
}

variable "internal_domain" {
  description = "Domain to use when creating internal dns records"
  type        = string
  default     = "ais-internal.com"
}

variable "s3_website_hosted_zone_id" {
  description = "The Amazon AWS hosted zone id for s3 website hosting"
  type        = map(string)
  default = {
    "us-east-1" = "Z3AQBSTGFYJSTF"
    "us-west-2" = "Z3BJ6K6RIION7M"
  }
}

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  type        = string
  default     = "CI0934608"
}

variable "internal_webstack_component_id" {
  description = "The Component ID of the internal-webstack"
  type        = string
  default     = "CI0941858"
}

variable "consumer_webstack_component_id" {
  description = "The Component ID of the consumer-webstack"
  type        = string
  default     = "CI0941859"
}

###############################################################
# Environment-Specific Variables (nonprod/prod overrides)
###############################################################

# Account configuration maps (same as cluster-infrastructure)
variable "account_config" {
  description = "Account-specific configuration"
  type = map(object({
    account_type = string
    account_id   = string
    account_name = string
    certificate_arn = string
    hosted_zone_id = string
    lambda_role_arn = string
    internal_hosted_zone_id = string
    external_hosted_zone_id = string
    external_domain = string
    alb_logs_bucket = string
    alb_logs_enabled = bool
    waf_request_limit = string
    rds_db_root_user = string
    rds_db_root_pw = string
    rds_multi_az = string
    rds_backup_retention = string
    rrri_db_cluster_instance_count = string
    rrri_db_instance_type = string
    include_rrri_db_deploy = bool
    incentives_cluster_replica_count = number
    incentives_cluster_replica_count_ca = number
  }))
  default = {
    "nonprod" = {
      account_type = "nonprod"
      account_id = "************"
      account_name = "awsaaianp"
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/278a49cb-11d5-4b49-937b-c79128597cf5"
      hosted_zone_id = "Z1VULNV75CDTF0"
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain = "coxais.com"
      alb_logs_bucket = "ais-nonprod-alb-logs"
      alb_logs_enabled = true
      waf_request_limit = "20000"
      rds_db_root_user = "aisadmin"
      rds_db_root_pw = "lightbomb"
      rds_multi_az = "false"
      rds_backup_retention = "7"
      rrri_db_cluster_instance_count = "1"
      rrri_db_instance_type = "db.r5.large"
      include_rrri_db_deploy = false
      incentives_cluster_replica_count = 0
      incentives_cluster_replica_count_ca = 0
    }
    "prod" = {
      account_type = "prod"
      account_id = "************"
      account_name = "awsaaiapd"
      certificate_arn = "arn:aws:acm:us-east-1:************:certificate/b8b5b5b5-1234-5678-9abc-************"
      hosted_zone_id = "Z1VULNV75CDTF0"
      lambda_role_arn = "arn:aws:iam::************:role/acct-managed/ais10-lambda-role"
      internal_hosted_zone_id = "Z1VULNV75CDTF0"
      external_hosted_zone_id = "Z11ZKVDNIDATM8"
      external_domain = "coxais.com"
      alb_logs_bucket = "ais-prod-alb-logs"
      alb_logs_enabled = true
      waf_request_limit = "50000"
      rds_db_root_user = "aisadmin"
      rds_db_root_pw = "lightbomb"
      rds_multi_az = "true"
      rds_backup_retention = "30"
      rrri_db_cluster_instance_count = "2"
      rrri_db_instance_type = "db.r5.xlarge"
      include_rrri_db_deploy = true
      incentives_cluster_replica_count = 1
      incentives_cluster_replica_count_ca = 1
    }
  }
}

# Package bucket names by environment and region
variable "package_bucket_names" {
  description = "The S3 bucket that code and INI should be pulled from"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ais.1-0.application.packages.np.ue1"
    }
    "prod" = {
      "us-east-1" = "ais.1-0.application.packages.pd.ue1"
      "us-west-2" = "ais.1-0.application.packages.pd.uw2"
    }
  }
}

# RDS backup bucket names by environment and region
variable "rds_backup_bucket_names" {
  description = "The S3 bucket that nightly rds backups store the dump to"
  type = map(map(string))
  default = {
    "nonprod" = {
      "us-east-1" = "ais.1-0.rds.backups.np.ue1"
    }
    "prod" = {
      "us-east-1" = "ais.1-0.rds.backups.pd.ue1"
      "us-west-2" = "ais.1-0.rds.backups.pd.uw2"
    }
  }
}

###############################################################
# Task-Specific Variables
###############################################################

# Cluster Configuration - Two ways to specify cluster
variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state"
  type        = string
  default     = ""
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state"
  type        = string
  default     = ""
}

# Option 2: Direct Cluster Configuration
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false)"
  type        = string
  default     = ""
}

# Task Definition Configuration
variable "task_friendly_name" {
  description = "Friendly name for the task"
  type        = string
}

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = "US"
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

# Container Configuration
variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

variable "dotnet_env" {
  description = "DotNet environment configuration"
  type        = string
  default     = ""
}

# Application-specific Configuration
variable "ini_bucket" {
  description = "S3 bucket for configuration files"
  type        = string
  default     = ""
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
  type        = string
  default     = ""
}

# Logging Configuration
variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = ""
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks"
  type        = string
  default     = ""
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
}

# Network Configuration (for Fargate tasks)
variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = false
}

# Monitoring and Alerting Configuration
variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = true
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm"
  type        = string
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = 1
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 1
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = 300
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (e.g., SNS topic)"
  type        = string
  default     = ""
}

###############################################################
# Variable Resolution Logic (Precedence: Region > Environment > Global)
###############################################################

locals {
  # Determine account type from environment
  account_type = contains(["prod", "production"], lower(var.environment)) ? "prod" : "nonprod"

  # Region abbreviation lookup
  region_abbreviated = lookup(var.regions_abbreviated, var.region, "unknown")

  # Account configuration resolution
  account_config = lookup(var.account_config, local.account_type, var.account_config["nonprod"])

  # Resolved variables with precedence logic
  package_bucket_name = lookup(
    lookup(var.package_bucket_names, local.account_type, {}),
    var.region,
    lookup(var.package_bucket_names["nonprod"], "us-east-1", "ais.1-0.application.packages.np.ue1")
  )

  rds_backup_bucket_name = lookup(
    lookup(var.rds_backup_bucket_names, local.account_type, {}),
    var.region,
    lookup(var.rds_backup_bucket_names["nonprod"], "us-east-1", "ais.1-0.rds.backups.np.ue1")
  )

  # Account-specific values
  account_id = local.account_config.account_id
  account_name = local.account_config.account_name
  certificate_arn = local.account_config.certificate_arn
  hosted_zone_id = local.account_config.hosted_zone_id
  lambda_role_arn = local.account_config.lambda_role_arn
  internal_hosted_zone_id = local.account_config.internal_hosted_zone_id
  external_hosted_zone_id = local.account_config.external_hosted_zone_id
  external_domain = local.account_config.external_domain

  # Set default values for application-specific configuration if not provided
  resolved_ini_bucket = var.ini_bucket != "" ? var.ini_bucket : local.package_bucket_name
  resolved_rds_backup_bucket = var.rds_backup_bucket != "" ? var.rds_backup_bucket : local.rds_backup_bucket_name
  resolved_dotnet_env = var.dotnet_env != "" ? var.dotnet_env : var.environment
}

# Cluster Configuration - Two ways to specify cluster
variable "use_remote_state" {
  description = "Whether to use Terraform remote state to get cluster information"
  type        = bool
  default     = true
}

# Option 1: Remote State Configuration
variable "cluster_state_bucket" {
  description = "S3 bucket containing cluster Terraform state"
  type        = string
  default     = ""
}

variable "cluster_state_key" {
  description = "S3 key for cluster Terraform state"
  type        = string
  default     = ""
}

# Option 2: Direct Cluster Configuration
variable "cluster_arn" {
  description = "ARN of the ECS cluster (used when use_remote_state is false)"
  type        = string
  default     = ""
}

variable "vpc_id" {
  description = "VPC ID (used when use_remote_state is false)"
  type        = string
  default     = ""
}

# Task Definition Configuration
variable "task_friendly_name" {
  description = "Friendly name for the task"
  type        = string
}

variable "country_iso_code" {
  description = "Country ISO code for the task"
  type        = string
  default     = "US"
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
}

variable "task_role_arn" {
  description = "ARN of the IAM role for the task"
  type        = string
}

variable "execution_role_arn" {
  description = "ARN of the IAM role for task execution"
  type        = string
}

variable "requires_compatibilities" {
  description = "Launch type compatibility (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

variable "launch_type" {
  description = "Launch type for the task (EC2 or FARGATE)"
  type        = string
  default     = "EC2"
}

# Container Configuration
variable "image_url_name_tag" {
  description = "Full URL of the container image including tag"
  type        = string
}

variable "dotnet_env" {
  description = "DotNet environment configuration"
  type        = string
  default     = ""
}

# Application-specific Configuration
variable "ini_bucket" {
  description = "S3 bucket for configuration files"
  type        = string
  default     = ""
}

variable "rds_backup_bucket" {
  description = "S3 bucket for RDS backups"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "ARN of the RRRI SNS topic"
  type        = string
  default     = ""
}

# Logging Configuration
variable "log_retention_in_days" {
  description = "CloudWatch log retention period in days"
  type        = number
  default     = 7
}

# Scheduling Configuration
variable "schedule_expression" {
  description = "CloudWatch Events schedule expression (empty string disables scheduling)"
  type        = string
  default     = ""
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "event_rule_arn" {
  description = "ARN of the IAM role for EventBridge to invoke ECS tasks"
  type        = string
  default     = ""
}

variable "task_count" {
  description = "Number of tasks to run when triggered"
  type        = number
  default     = 1
}

# Network Configuration (for Fargate tasks)
variable "subnet_ids" {
  description = "List of subnet IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "security_group_ids" {
  description = "List of security group IDs for Fargate tasks"
  type        = list(string)
  default     = []
}

variable "assign_public_ip" {
  description = "Whether to assign public IP to Fargate tasks"
  type        = bool
  default     = false
}

# Monitoring and Alerting Configuration
variable "create_failure_alarm" {
  description = "Whether to create CloudWatch alarm for task failures"
  type        = bool
  default     = true
}

variable "alarm_description" {
  description = "Description for the CloudWatch alarm"
  type        = string
  default     = ""
}

variable "alarm_metric_filter_pattern" {
  description = "Log metric filter pattern for detecting errors"
  type        = string
  default     = "[timestamp, request_id, level=\"ERROR\", ...]"
}

variable "alarm_failure_threshold" {
  description = "Threshold for failure alarm"
  type        = number
  default     = 1
}

variable "alarm_evaluation_periods" {
  description = "Number of evaluation periods for the alarm"
  type        = number
  default     = 1
}

variable "alarm_period" {
  description = "Period for alarm evaluation in seconds"
  type        = number
  default     = 300
}

variable "alarm_action_arn" {
  description = "ARN of the action to take when alarm is triggered (e.g., SNS topic)"
  type        = string
  default     = ""
}