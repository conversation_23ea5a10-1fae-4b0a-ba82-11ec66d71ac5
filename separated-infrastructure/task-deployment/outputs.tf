# Task Definition Outputs
output "task_definition_arn" {
  description = "ARN of the ECS task definition"
  value       = aws_ecs_task_definition.main.arn
}

output "task_definition_family" {
  description = "Family name of the ECS task definition"
  value       = aws_ecs_task_definition.main.family
}

output "task_definition_revision" {
  description = "Revision number of the ECS task definition"
  value       = aws_ecs_task_definition.main.revision
}

# CloudWatch Log Group Outputs
output "log_group_name" {
  description = "Name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.task_logs.name
}

output "log_group_arn" {
  description = "ARN of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.task_logs.arn
}

# Scheduling Outputs
output "scheduled_rule_name" {
  description = "Name of the CloudWatch Events rule (if scheduling is enabled)"
  value       = var.schedule_expression != "" ? aws_cloudwatch_event_rule.scheduled_task[0].name : null
}

output "scheduled_rule_arn" {
  description = "ARN of the CloudWatch Events rule (if scheduling is enabled)"
  value       = var.schedule_expression != "" ? aws_cloudwatch_event_rule.scheduled_task[0].arn : null
}

output "schedule_enabled" {
  description = "Whether the task scheduling is enabled"
  value       = var.enabled && var.schedule_expression != ""
}

# Monitoring Outputs
output "failure_alarm_name" {
  description = "Name of the failure CloudWatch alarm (if created)"
  value       = var.create_failure_alarm ? aws_cloudwatch_metric_alarm.task_failure[0].alarm_name : null
}

output "failure_alarm_arn" {
  description = "ARN of the failure CloudWatch alarm (if created)"
  value       = var.create_failure_alarm ? aws_cloudwatch_metric_alarm.task_failure[0].arn : null
}

output "log_metric_filter_name" {
  description = "Name of the log metric filter (if created)"
  value       = var.create_failure_alarm && var.alarm_metric_filter_pattern != "" ? aws_cloudwatch_log_metric_filter.error_filter[0].name : null
}

# Configuration Outputs
output "task_friendly_name" {
  description = "Friendly name of the task"
  value       = var.task_friendly_name
}

output "environment" {
  description = "Environment name"
  value       = var.environment
}

output "launch_type" {
  description = "Launch type used for the task"
  value       = var.launch_type
}

output "cluster_arn" {
  description = "ARN of the ECS cluster being used"
  value       = local.cluster_arn
}

# Container Configuration Outputs
output "image_url" {
  description = "Container image URL being used"
  value       = var.image_url_name_tag
}

output "container_definition_path" {
  description = "Path to the container definition file"
  value       = var.container_definition_path
}
