terraform {
  required_providers {
    aws = {
      version = "~> 3.75"
      source  = "hashicorp/aws"
    }
  }
  backend "s3" {}
}

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.component_id
      Application     = local.application
      Environment     = var.environment
      Service         = local.service
      Component       = var.component
    }
  }
  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# Data source to get cluster information (if using remote state)
data "terraform_remote_state" "cluster" {
  count   = var.use_remote_state ? 1 : 0
  backend = "s3"

  config = {
    bucket = var.cluster_state_bucket
    key    = var.cluster_state_key
    region = var.region
  }
}

# Local values to handle both remote state and direct input scenarios
locals {
  cluster_arn = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.cluster_arn : var.cluster_arn
  vpc_id      = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.vpc_id : var.vpc_id
  region      = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.region : var.region
}

# ECS Task Definition
resource "aws_ecs_task_definition" "main" {
  family                   = "${var.task_friendly_name}_${var.environment}"
  container_definitions    = data.template_file.container_definition.rendered
  task_role_arn            = var.task_role_arn
  execution_role_arn       = var.execution_role_arn
  requires_compatibilities = [var.requires_compatibilities]

  volume {
    name      = "aisdata"
    host_path = "/aisdata"
  }

  tags = {
    Application  = local.application
    Environment  = var.environment
    Service      = local.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = local.slack_contact
    Component    = var.component
    TaskName     = var.task_friendly_name
  }
}

# Container definition template
data "template_file" "container_definition" {
  template = file(var.container_definition_path)
  vars = {
    environment        = var.environment
    country_iso_code   = var.country_iso_code
    region             = local.region
    task_friendly_name = var.task_friendly_name
    image_url_name_tag = var.image_url_name_tag
    ini_bucket         = local.resolved_ini_bucket
    rds_backup_bucket  = local.resolved_rds_backup_bucket
    rrri_topic_arn     = var.rrri_topic_arn
    dotnet_env         = local.resolved_dotnet_env
  }
}

# CloudWatch Log Group for the task
resource "aws_cloudwatch_log_group" "task_logs" {
  name              = "${var.task_friendly_name}_${var.environment}"
  retention_in_days = var.log_retention_in_days

  tags = {
    Application  = local.application
    Environment  = var.environment
    Service      = local.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = local.slack_contact
    Component    = var.component
    TaskName     = var.task_friendly_name
  }
}

# EventBridge rule for scheduled execution
resource "aws_cloudwatch_event_rule" "scheduled_task" {
  count = var.schedule_expression != "" ? 1 : 0

  name                = "${var.task_friendly_name}-${var.environment}-schedule"
  description         = "Scheduled execution for ${var.task_friendly_name}"
  schedule_expression = var.schedule_expression
  state               = var.enabled ? "ENABLED" : "DISABLED"

  tags = {
    Application  = local.application
    Environment  = var.environment
    Service      = local.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = local.slack_contact
    Component    = var.component
    TaskName     = var.task_friendly_name
  }
}

# EventBridge target to run the ECS task
resource "aws_cloudwatch_event_target" "ecs_task" {
  count = var.schedule_expression != "" ? 1 : 0

  rule     = aws_cloudwatch_event_rule.scheduled_task[0].name
  arn      = local.cluster_arn
  role_arn = var.event_rule_arn

  ecs_target {
    task_definition_arn = aws_ecs_task_definition.main.arn
    task_count          = var.task_count
    launch_type         = var.launch_type

    # Network configuration for Fargate tasks
    dynamic "network_configuration" {
      for_each = var.launch_type == "FARGATE" ? [1] : []
      content {
        subnets          = var.subnet_ids
        security_groups  = var.security_group_ids
        assign_public_ip = var.assign_public_ip
      }
    }
  }
}

# CloudWatch Alarm for task failures
resource "aws_cloudwatch_metric_alarm" "task_failure" {
  count = var.create_failure_alarm ? 1 : 0

  alarm_name        = "${var.task_friendly_name}-${var.environment}-failures"
  alarm_description = local.resolved_alarm_description

  metric_name = "ErrorCount"
  namespace   = "AWS/Logs"
  statistic   = "Sum"

  comparison_operator = "GreaterThanThreshold"
  threshold           = var.alarm_failure_threshold
  evaluation_periods  = var.alarm_evaluation_periods
  period              = var.alarm_period

  dimensions = {
    LogGroupName = aws_cloudwatch_log_group.task_logs.name
  }

  alarm_actions = var.alarm_action_arn != "" ? [var.alarm_action_arn] : []

  tags = {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
    Component    = var.component
    TaskName     = var.task_friendly_name
  }
}

# CloudWatch Log Metric Filter for error detection
resource "aws_cloudwatch_log_metric_filter" "error_filter" {
  count = var.create_failure_alarm && var.alarm_metric_filter_pattern != "" ? 1 : 0

  name           = "${var.task_friendly_name}-${var.environment}-error-filter"
  log_group_name = aws_cloudwatch_log_group.task_logs.name
  pattern        = var.alarm_metric_filter_pattern

  metric_transformation {
    name      = "ErrorCount"
    namespace = "AWS/Logs"
    value     = "1"
  }
}
