#==============================================================
# Computed Variables and Resolution Logic
# This file implements the variable precedence logic:
# Region-specific > Environment-specific > Global
#==============================================================

locals {
  # Determine account type from environment
  account_type = contains(["prod", "production"], lower(var.environment)) ? "prod" : "nonprod"
  
  # Get environment configuration
  environment_config = lookup(local.environment_configs, local.account_type, local.environment_configs["nonprod"])
  environment_resources = lookup(local.environment_resources, local.account_type, local.environment_resources["nonprod"])
  
  #==============================================================
  # Resolved Configuration Values
  # These are the final computed values used by the task deployment
  #==============================================================
  
  # Core application configuration
  application             = local.global_config.application
  application_abbreviated = local.global_config.application_abbreviated
  service                 = local.global_config.service
  slack_contact          = local.global_config.slack_contact
  
  # Region configuration
  region_abbreviated = lookup(local.region_config.abbreviations, var.region, "unknown")
  
  # Account-specific configuration
  account_id                  = local.environment_config.account_id
  account_name               = local.environment_config.account_name
  certificate_arn            = local.environment_config.certificate_arn
  hosted_zone_id             = local.environment_config.hosted_zone_id
  lambda_role_arn            = local.environment_config.lambda_role_arn
  internal_hosted_zone_id    = local.environment_config.internal_hosted_zone_id
  external_hosted_zone_id    = local.environment_config.external_hosted_zone_id
  external_domain            = local.environment_config.external_domain
  
  # S3 Bucket resolution (environment + region specific)
  package_bucket_name = lookup(
    local.environment_resources.package_buckets,
    var.region,
    lookup(local.environment_resources.package_buckets, "us-east-1", "ais.1-0.application.packages.np.ue1")
  )
  
  rds_backup_bucket_name = lookup(
    local.environment_resources.rds_backup_buckets,
    var.region,
    lookup(local.environment_resources.rds_backup_buckets, "us-east-1", "ais.1-0.rds.backups.np.ue1")
  )
  
  #==============================================================
  # Task-Specific Resolved Values
  # These handle defaults and overrides for task configuration
  #==============================================================
  
  # Set default values for application-specific configuration if not provided
  resolved_ini_bucket = var.ini_bucket != "" ? var.ini_bucket : local.package_bucket_name
  resolved_rds_backup_bucket = var.rds_backup_bucket != "" ? var.rds_backup_bucket : local.rds_backup_bucket_name
  resolved_dotnet_env = var.dotnet_env != "" ? var.dotnet_env : var.environment
  
  # Cluster configuration resolution
  cluster_arn = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.cluster_arn : var.cluster_arn
  vpc_id      = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.vpc_id : var.vpc_id
  region      = var.use_remote_state ? data.terraform_remote_state.cluster[0].outputs.region : var.region
  
  # Alarm configuration with defaults
  resolved_alarm_description = var.alarm_description != "" ? var.alarm_description : "Alarm for ${var.task_friendly_name} task failures"
}
