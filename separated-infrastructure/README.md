# Separated ECS Infrastructure

This project provides a modular approach to AWS ECS infrastructure management with clear separation of concerns between cluster infrastructure and application deployments.

## Overview

The infrastructure is separated into two main components:

1. **Cluster Infrastructure Module** (`cluster-infrastructure/`) - One-time setup of ECS cluster and supporting infrastructure
2. **Task Deployment Module** (`task-deployment/`) - Application-specific task definitions and configurations

This separation enables:
- **Reusability**: One cluster can host multiple applications
- **Independent Scaling**: Applications can be deployed/updated independently
- **Cost Efficiency**: Shared infrastructure reduces overhead
- **Clear Ownership**: Infrastructure and application teams can work independently
- **Simplified Management**: Easier to maintain and troubleshoot

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Cluster Infrastructure                   │
│                    (Deploy Once Per Environment)            │
├─────────────────────────────────────────────────────────────┤
│  • ECS Cluster                                              │
│  • Auto Scaling Group                                       │
│  • Security Groups                                          │
│  • CloudWatch Alarms                                        │
│  • Lambda Functions                                         │
│  • CloudWatch Events                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Task Deployments                         │
│                (Deploy Per Application)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐  │
│  │   Application   │  │   Application   │  │     ...     │  │
│  │       A         │  │       B         │  │             │  │
│  │                 │  │                 │  │             │  │
│  │ • Task Def      │  │ • Task Def      │  │ • Task Def  │  │
│  │ • Log Groups    │  │ • Log Groups    │  │ • Log Groups│  │
│  │ • Scheduling    │  │ • Scheduling    │  │ • Scheduling│  │
│  │ • Monitoring    │  │ • Monitoring    │  │ • Monitoring│  │
│  └─────────────────┘  └─────────────────┘  └─────────────┘  │
└─────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
separated-infrastructure/
├── cluster-infrastructure/          # ECS Cluster Infrastructure Module
│   ├── main.tf                     # Main cluster resources
│   ├── cloudwatch.tf               # CloudWatch alarms and monitoring
│   ├── lambda.tf                   # Lambda functions
│   ├── variables.tf                # Input variables
│   ├── outputs.tf                  # Output values
│   ├── userdata.tpl                # EC2 user data template
│   └── README.md                   # Module documentation
│
├── task-deployment/                 # Task Deployment Module
│   ├── main.tf                     # Task definition and scheduling
│   ├── variables.tf                # Input variables
│   ├── outputs.tf                  # Output values
│   ├── container-definitions/      # Sample container definitions
│   │   └── sample-dotnet-task.json
│   └── README.md                   # Module documentation
│
├── examples/                        # Usage examples
│   └── complete-setup/             # Complete deployment example
│       ├── main.tf                 # Example using both modules
│       ├── variables.tf            # Example variables
│       ├── outputs.tf              # Example outputs
│       ├── terraform.tfvars.example # Sample configuration
│       ├── container-definitions/  # Example container definitions
│       └── README.md               # Example documentation
│
└── README.md                       # This file
```

## Quick Start

### 1. Deploy Cluster Infrastructure (One-time per environment)

```bash
cd cluster-infrastructure

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

### 2. Deploy Application Tasks (Per application)

```bash
cd task-deployment

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

### 3. Use Complete Example (Recommended for first-time users)

```bash
cd examples/complete-setup

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy everything together
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

## Key Benefits

### 1. Separation of Concerns
- **Infrastructure Team**: Manages cluster infrastructure
- **Application Team**: Manages task definitions and deployments
- **Clear Boundaries**: Well-defined interfaces between components

### 2. Cost Efficiency
- **Shared Resources**: Multiple applications share the same cluster
- **Auto Scaling**: Cluster scales based on aggregate demand
- **Scheduled Scaling**: Automatic scale-down during off-hours

### 3. Operational Excellence
- **Independent Deployments**: Applications can be deployed without affecting others
- **Centralized Monitoring**: Cluster-level monitoring and alerting
- **Consistent Patterns**: Standardized deployment patterns across applications

### 4. Flexibility
- **Multiple Launch Types**: Support for both EC2 and Fargate
- **Scheduling Options**: Cron-based and rate-based scheduling
- **Custom Configurations**: Extensive customization options

## Integration Patterns

### Pattern 1: Remote State Integration (Recommended)

```hcl
# Deploy cluster infrastructure first
module "cluster" {
  source = "./cluster-infrastructure"
  # ... configuration
}

# Deploy tasks using remote state
module "task" {
  source = "./task-deployment"
  
  use_remote_state     = true
  cluster_state_bucket = "my-terraform-state"
  cluster_state_key    = "cluster/terraform.tfstate"
  # ... configuration
}
```

### Pattern 2: Direct Module Integration

```hcl
# Deploy both in the same Terraform configuration
module "cluster" {
  source = "./cluster-infrastructure"
  # ... configuration
}

module "task" {
  source = "./task-deployment"
  
  use_remote_state = false
  cluster_arn      = module.cluster.cluster_arn
  vpc_id           = module.cluster.vpc_id
  # ... configuration
}
```

### Pattern 3: Separate Terraform Configurations

```bash
# Deploy cluster infrastructure
cd cluster-infrastructure
terraform apply

# Deploy tasks in separate configurations
cd ../task-deployment
terraform apply
```

## Migration from Existing Infrastructure

If you're migrating from the existing monolithic infrastructure:

### Step 1: Analyze Current Setup
1. Review existing `infrastructure/modules/processing-apps/ecs-cluster/`
2. Review existing `infrastructure/modules/processing-apps/task-definitions/`
3. Identify shared vs. application-specific resources

### Step 2: Deploy New Cluster Infrastructure
1. Use the `cluster-infrastructure` module
2. Configure with existing VPC, subnets, and security groups
3. Migrate auto scaling and monitoring configurations

### Step 3: Migrate Task Definitions
1. Use the `task-deployment` module
2. Copy existing container definitions
3. Configure scheduling and monitoring

### Step 4: Validate and Switch
1. Test new infrastructure in non-production
2. Validate all functionality works as expected
3. Plan migration strategy for production

## Best Practices

### 1. State Management
- Use remote state backends (S3 + DynamoDB)
- Separate state files for cluster and applications
- Use state locking to prevent conflicts

### 2. Security
- Use least-privilege IAM roles
- Deploy in private subnets
- Enable VPC Flow Logs and CloudTrail
- Use AWS Secrets Manager for sensitive data

### 3. Monitoring
- Enable comprehensive CloudWatch monitoring
- Set up proper alerting channels
- Use structured logging
- Monitor both infrastructure and application metrics

### 4. Cost Management
- Use auto scaling schedules
- Right-size instance types
- Monitor and optimize log retention
- Use Spot instances where appropriate

### 5. Deployment
- Use CI/CD pipelines for deployments
- Implement proper testing strategies
- Use blue-green deployments for zero-downtime updates
- Maintain deployment documentation

## Troubleshooting

### Common Issues

1. **Tasks not starting**: Check IAM roles, security groups, and resource limits
2. **Scheduling not working**: Verify EventBridge rules and IAM permissions
3. **Logs not appearing**: Check CloudWatch log group configuration
4. **Auto scaling issues**: Review CloudWatch alarms and scaling policies

### Debugging Commands

```bash
# Check cluster status
aws ecs describe-clusters --clusters CLUSTER-NAME

# List running tasks
aws ecs list-tasks --cluster CLUSTER-NAME

# View task logs
aws logs tail LOG-GROUP-NAME --follow

# Check EventBridge rules
aws events list-rules --name-prefix RULE-PREFIX
```

## Contributing

1. Follow Terraform best practices
2. Update documentation for any changes
3. Test changes in non-production environments
4. Use consistent naming conventions
5. Add examples for new features

## Support

For questions or issues:
1. Check module-specific README files
2. Review the complete example
3. Check AWS ECS documentation
4. Review Terraform AWS provider documentation

## License

This project follows the same license as the parent repository.
