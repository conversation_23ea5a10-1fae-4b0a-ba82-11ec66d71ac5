# Separated ECS Infrastructure

This project provides a modular approach to AWS ECS infrastructure management with clear separation of concerns between cluster infrastructure and application deployments.

## Overview

The infrastructure is separated into two main components:

1. **Cluster Infrastructure Module** (`cluster-infrastructure/`) - One-time setup of ECS cluster and supporting infrastructure
2. **Task Deployment Module** (`task-deployment/`) - Application-specific task definitions and configurations

This separation enables:
- **Reusability**: One cluster can host multiple applications
- **Independent Scaling**: Applications can be deployed/updated independently
- **Cost Efficiency**: Shared infrastructure reduces overhead
- **Clear Ownership**: Infrastructure and application teams can work independently
- **Simplified Management**: Easier to maintain and troubleshoot

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Cluster Infrastructure                   │
│                    (Deploy Once Per Environment)           │
├─────────────────────────────────────────────────────────────┤
│  • ECS Cluster                                              │
│  • Auto Scaling Group                                       │
│  • Security Groups                                          │
│  • CloudWatch Alarms                                        │
│  • Lambda Functions                                         │
│  • CloudWatch Events                                        │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    Task Deployments                        │
│                (Deploy Per Application)                     │
├─────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────┐ │
│  │   Application   │  │   Application   │  │     ...     │ │
│  │       A         │  │       B         │  │             │ │
│  │                 │  │                 │  │             │ │
│  │ • Task Def      │  │ • Task Def      │  │ • Task Def  │ │
│  │ • Log Groups    │  │ • Log Groups    │  │ • Log Groups│ │
│  │ • Scheduling    │  │ • Scheduling    │  │ • Scheduling│ │
│  │ • Monitoring    │  │ • Monitoring    │  │ • Monitoring│ │
│  └─────────────────┘  └─────────────────┘  └─────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## Directory Structure

```
separated-infrastructure/
├── cluster-infrastructure/          # ECS Cluster Infrastructure Module
│   ├── main.tf                     # Main cluster resources
│   ├── cloudwatch.tf               # CloudWatch alarms and monitoring
│   ├── lambda.tf                   # Lambda functions
│   ├── variables.tf                # Input variables
│   ├── outputs.tf                  # Output values
│   ├── userdata.tpl                # EC2 user data template
│   └── README.md                   # Module documentation
│
├── task-deployment/                 # Task Deployment Module
│   ├── main.tf                     # Task definition and scheduling
│   ├── variables.tf                # Input variables
│   ├── outputs.tf                  # Output values
│   ├── container-definitions/      # Sample container definitions
│   │   └── sample-dotnet-task.json
│   └── README.md                   # Module documentation
│
├── examples/                        # Usage examples
│   └── complete-setup/             # Complete deployment example
│       ├── main.tf                 # Example using both modules
│       ├── variables.tf            # Example variables
│       ├── outputs.tf              # Example outputs
│       ├── terraform.tfvars.example # Sample configuration
│       ├── container-definitions/  # Example container definitions
│       └── README.md               # Example documentation
│
└── README.md                       # This file
```

## Quick Start

### 1. Deploy Cluster Infrastructure (One-time per environment)

```bash
cd cluster-infrastructure

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

### 2. Deploy Application Tasks (Per application)

```bash
cd task-deployment

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

### 3. Use Complete Example (Recommended for first-time users)

```bash
cd examples/complete-setup

# Configure variables
cp terraform.tfvars.example terraform.tfvars
# Edit terraform.tfvars with your values

# Deploy everything together
terraform init
terraform plan -var-file="terraform.tfvars"
terraform apply -var-file="terraform.tfvars"
```

## Key Benefits

### 1. Separation of Concerns
- **Infrastructure Team**: Manages cluster infrastructure
- **Application Team**: Manages task definitions and deployments
- **Clear Boundaries**: Well-defined interfaces between components

### 2. Cost Efficiency
- **Shared Resources**: Multiple applications share the same cluster
- **Auto Scaling**: Cluster scales based on aggregate demand
- **Scheduled Scaling**: Automatic scale-down during off-hours

### 3. Operational Excellence
- **Independent Deployments**: Applications can be deployed without affecting others
- **Centralized Monitoring**: Cluster-level monitoring and alerting
- **Consistent Patterns**: Standardized deployment patterns across applications

### 4. Flexibility
- **Multiple Launch Types**: Support for both EC2 and Fargate
- **Scheduling Options**: Cron-based and rate-based scheduling
- **Custom Configurations**: Extensive customization options

## Integration Patterns

### Pattern 1: Remote State Integration (Recommended)

```hcl
# Deploy cluster infrastructure first
module "cluster" {
  source = "./cluster-infrastructure"
  # ... configuration
}

# Deploy tasks using remote state
module "task" {
  source = "./task-deployment"
  
  use_remote_state     = true
  cluster_state_bucket = "my-terraform-state"
  cluster_state_key    = "cluster/terraform.tfstate"
  # ... configuration
}
```

### Pattern 2: Direct Module Integration

```hcl
# Deploy both in the same Terraform configuration
module "cluster" {
  source = "./cluster-infrastructure"
  # ... configuration
}

module "task" {
  source = "./task-deployment"
  
  use_remote_state = false
  cluster_arn      = module.cluster.cluster_arn
  vpc_id           = module.cluster.vpc_id
  # ... configuration
}
```

### Pattern 3: Separate Terraform Configurations

```bash
# Deploy cluster infrastructure
cd cluster-infrastructure
terraform apply

# Deploy tasks in separate configurations
cd ../task-deployment
terraform apply
```

## Migration from Existing Infrastructure

If you're migrating from the existing monolithic infrastructure, we provide a comprehensive migration guide and validation tools:

### Quick Migration Overview

1. **Validate Compatibility**: Run the validation script
   ```bash
   cd separated-infrastructure
   ./validate-compatibility.sh
   ```

2. **Follow Migration Guide**: See [MIGRATION-GUIDE.md](MIGRATION-GUIDE.md) for detailed steps

3. **Import Existing Resources**: Use Terraform import to avoid recreation
   ```bash
   terraform import aws_ecs_cluster.main existing-cluster-name
   ```

4. **Gradual Migration**: Deploy new modules alongside existing infrastructure

### Key Compatibility Features

- ✅ **100% Resource Name Compatibility**: No AWS resources need recreation
- ✅ **Variable Compatibility**: Existing terraform.tfvars work with minimal changes
- ✅ **Functional Parity**: All original capabilities preserved
- ✅ **Zero Downtime Migration**: Import existing resources without disruption

### Migration Tools

- **Validation Script**: `validate-compatibility.sh` - Comprehensive compatibility checks
- **Migration Guide**: `MIGRATION-GUIDE.md` - Step-by-step migration instructions
- **Variable Mapping**: Complete mapping between original and separated variables

## GitHub Actions Workflows

The separated infrastructure includes comprehensive GitHub Actions workflows that completely replace the original Cake build system with native GitHub Actions implementation:

### 🔄 Complete Migration from Cake Build System

#### Original Cake System Components Replaced:
- ✅ **`deployTerraformComponent.cake`** → Native GitHub Actions workflows
- ✅ **`cake.ps1` PowerShell bootstrapper** → GitHub Actions runners
- ✅ **ALKS credential management** → AWS IAM roles/GitHub secrets
- ✅ **Component configuration JSON** → GitHub variables and workflow inputs
- ✅ **Lambda packaging with NPM** → Composite actions with Node.js setup
- ✅ **Terraform shared variable management** → Dynamic tfvars generation

### Available Workflows

1. **Deploy Cluster Infrastructure** (`.github/workflows/deploy-cluster-infrastructure.yml`)
   - Deploys ECS cluster, auto scaling, monitoring, and Lambda components
   - Triggered manually or on cluster infrastructure changes
   - Includes Lambda packaging and artifact management

2. **Deploy Task Definitions** (`.github/workflows/deploy-task-definitions.yml`)
   - Deploys ECS task definitions, scheduling, and application-specific configurations
   - Triggered manually or on task deployment changes
   - Supports SSM Parameter Store integration

3. **Deploy Complete Infrastructure** (`.github/workflows/deploy-complete-infrastructure.yml`)
   - Orchestrates both cluster and task deployments
   - Matrix strategy for multiple task deployments
   - End-to-end deployment with comprehensive status reporting

### Key Features

- **🚀 Native GitHub Actions**: Zero dependencies on Cake build system
- **🔄 100% Parameter Compatibility**: All original Cake parameters preserved and mapped
- **🔒 Enhanced Security**: Integrated Checkov scanning, secret management, and access controls
- **♻️ Reusable Components**: Composite actions and reusable workflows for shared logic
- **📊 Comprehensive Monitoring**: Detailed deployment status, summaries, and Slack notifications
- **🎯 Matrix Deployments**: Parallel task deployments with failure isolation

### Migration Benefits

| Aspect | Original Cake System | New GitHub Actions |
|--------|---------------------|-------------------|
| **Dependencies** | Cake, PowerShell, ALKS, NuGet | Native GitHub Actions only |
| **Security** | Manual credential management | Built-in secret management + scanning |
| **Maintainability** | Custom Cake scripts | Standard GitHub Actions patterns |
| **CI/CD Integration** | External build system | Native GitHub integration |
| **Error Handling** | Basic exit codes | Comprehensive status reporting |
| **Parallel Execution** | Sequential only | Matrix strategies supported |

See [GitHub Actions Documentation](.github/workflows/README.md) for detailed usage instructions and migration guide.

## Best Practices

### 1. State Management
- Use remote state backends (S3 + DynamoDB)
- Separate state files for cluster and applications
- Use state locking to prevent conflicts

### 2. Security
- Use least-privilege IAM roles
- Deploy in private subnets
- Enable VPC Flow Logs and CloudTrail
- Use AWS Secrets Manager for sensitive data

### 3. Monitoring
- Enable comprehensive CloudWatch monitoring
- Set up proper alerting channels
- Use structured logging
- Monitor both infrastructure and application metrics

### 4. Cost Management
- Use auto scaling schedules
- Right-size instance types
- Monitor and optimize log retention
- Use Spot instances where appropriate

### 5. Deployment
- Use CI/CD pipelines for deployments
- Implement proper testing strategies
- Use blue-green deployments for zero-downtime updates
- Maintain deployment documentation

## Troubleshooting

### Common Issues

1. **Tasks not starting**: Check IAM roles, security groups, and resource limits
2. **Scheduling not working**: Verify EventBridge rules and IAM permissions
3. **Logs not appearing**: Check CloudWatch log group configuration
4. **Auto scaling issues**: Review CloudWatch alarms and scaling policies

### Debugging Commands

```bash
# Check cluster status
aws ecs describe-clusters --clusters CLUSTER-NAME

# List running tasks
aws ecs list-tasks --cluster CLUSTER-NAME

# View task logs
aws logs tail LOG-GROUP-NAME --follow

# Check EventBridge rules
aws events list-rules --name-prefix RULE-PREFIX
```

## Contributing

1. Follow Terraform best practices
2. Update documentation for any changes
3. Test changes in non-production environments
4. Use consistent naming conventions
5. Add examples for new features

## Support

For questions or issues:
1. Check module-specific README files
2. Review the complete example
3. Check AWS ECS documentation
4. Review Terraform AWS provider documentation

## License

This project follows the same license as the parent repository.
